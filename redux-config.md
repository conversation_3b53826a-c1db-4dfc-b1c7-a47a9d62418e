# Configuration Redux Offline + Firebase - Guide Complet

## ✅ Ce qui a été intégré

### 1. **Structure Redux complète**
```
src/store/
├── index.ts                 # Configuration du store principal avec persistance
├── hooks.ts                 # Hooks typés et sélecteurs personnalisés
└── slices/                  # Slices Redux Toolkit
    ├── authSlice.ts        # Authentification utilisateur
    ├── sessionsSlice.ts    # Sessions de méditation
    ├── journalSlice.ts     # Entrées de journal
    ├── audioAssetsSlice.ts # Assets audio
    ├── syncSlice.ts        # État de synchronisation
    └── networkSlice.ts     # État réseau
```

### 2. **Fonctionnalités implémentées**
- ✅ **Persistance locale** : Toutes les données sont sauvegardées automatiquement
- ✅ **Store Redux** configuré avec Redux Toolkit
- ✅ **Hooks typés** pour TypeScript
- ✅ **Actions asynchrones** pour charger les données
- ✅ **Composant de statut de sync** dans l'en-tête
- ✅ **Intégration dans l'application** (Provider, PersistGate)
- ✅ **Services Firebase** améliorés

### 3. **Dépendances installées**
- `redux-persist` : Persistance locale
- `redux-persist-transform-encrypt` : Chiffrement (à configurer)

## 🔧 Configuration à faire de votre côté

### 1. **Configuration Firebase**

Créez un fichier `.env` à la racine du projet :

```bash
# Configuration Firebase
REACT_APP_FIREBASE_API_KEY=votre_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=votre_projet.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=votre_projet_id
REACT_APP_FIREBASE_STORAGE_BUCKET=votre_projet.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=votre_sender_id
REACT_APP_FIREBASE_APP_ID=votre_app_id

# Clé de chiffrement (changez en production !)
REACT_APP_ENCRYPT_KEY=votre_cle_secrete_de_chiffrement

# Émulateurs Firebase (développement)
REACT_APP_USE_FIREBASE_EMULATOR=false
```

### 2. **Règles Firestore**

Configurez les règles de sécurité dans Firebase Console :

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Sessions utilisateur
    match /sessions/{sessionId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Entrées de journal
    match /journal_entries/{entryId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Assets audio utilisateur
    match /audio_assets/{assetId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
  }
}
```

### 3. **Authentification Firebase**

Activez l'authentification dans Firebase Console :
- Email/Password
- Google (optionnel)
- Autres providers selon vos besoins

### 4. **Implémentation de l'authentification**

Complétez le slice d'authentification dans `src/store/slices/authSlice.ts` :

```typescript
import { signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { auth } from '../../services/firebase';

// Dans signInUser
const userCredential = await signInWithEmailAndPassword(auth, credentials.email, credentials.password);
return userCredential.user;

// Dans signUpUser  
const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
return userCredential.user;

// Dans signOutUser
await signOut(auth);
```

### 5. **Synchronisation Firebase complète**

Implémentez les services de synchronisation dans `src/services/syncService.ts` :

```typescript
// Exemple pour les sessions
export const syncSessionsToFirebase = async (sessions: Session[]) => {
  const batch = writeBatch(db);
  
  sessions.forEach(session => {
    const sessionRef = doc(db, 'sessions', session.id);
    batch.set(sessionRef, {
      ...session,
      userId: auth.currentUser?.uid,
      updatedAt: serverTimestamp()
    });
  });
  
  await batch.commit();
};
```

## 🚀 Utilisation dans vos composants

### 1. **Hooks de base**

```typescript
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchSessions, createSession } from '../store/slices/sessionsSlice';

const MonComposant = () => {
  const dispatch = useAppDispatch();
  const { sessions, isLoading, error } = useSessions();
  
  // Charger les sessions
  useEffect(() => {
    dispatch(fetchSessions());
  }, [dispatch]);
  
  // Créer une session
  const handleCreate = async () => {
    try {
      await dispatch(createSession(newSessionData)).unwrap();
    } catch (error) {
      console.error('Erreur:', error);
    }
  };
};
```

### 2. **Hooks spécialisés**

```typescript
import { useSessions, useSync, useOfflineStatus } from '../store/hooks';

const MonComposant = () => {
  const { sessions, isLoading } = useSessions();
  const { isSyncing, syncErrors } = useSync();
  const { isOffline, hasPendingChanges } = useOfflineStatus();
  
  return (
    <div>
      {isOffline && <div>Mode hors ligne</div>}
      {hasPendingChanges && <div>Changements en attente</div>}
      {/* Votre contenu */}
    </div>
  );
};
```

### 3. **Gestion des erreurs**

```typescript
const MonComposant = () => {
  const { error } = useSessions();
  
  if (error) {
    return <div className="error">Erreur: {error}</div>;
  }
  
  // Contenu normal
};
```

## 📱 Fonctionnalités avancées à implémenter

### 1. **Middleware de synchronisation automatique**

Recréez `src/store/middleware/syncMiddleware.ts` avec une meilleure gestion des types :

```typescript
import { Middleware, AnyAction } from '@reduxjs/toolkit';
import { RootState } from '../index';

export const syncMiddleware: Middleware<{}, RootState> = (store) => (next) => (action: AnyAction) => {
  const result = next(action);
  
  // Logique de synchronisation
  if (action.type.endsWith('/fulfilled') && navigator.onLine) {
    // Déclencher une synchronisation
  }
  
  return result;
};
```

### 2. **Gestion des conflits**

```typescript
const resolveConflict = (localData: any, remoteData: any) => {
  // Stratégies de résolution :
  // 1. Last-write-wins (par timestamp)
  // 2. Merge intelligent
  // 3. Demander à l'utilisateur
  
  if (localData.updatedAt > remoteData.updatedAt) {
    return localData;
  }
  return remoteData;
};
```

### 3. **Synchronisation en arrière-plan**

```typescript
// Service Worker pour sync en arrière-plan
// Dans public/sw.js
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(syncPendingChanges());
  }
});
```

### 4. **Optimisation des performances**

```typescript
// Sélecteurs mémorisés
import { createSelector } from '@reduxjs/toolkit';

const selectSessionsByCategory = createSelector(
  [(state: RootState) => state.sessions.sessions, (_, category: string) => category],
  (sessions, category) => sessions.filter(s => s.category === category)
);
```

## 🧪 Tests

### 1. **Tests des slices**

```typescript
import { configureStore } from '@reduxjs/toolkit';
import sessionsSlice, { fetchSessions } from '../slices/sessionsSlice';

describe('sessionsSlice', () => {
  it('should handle fetchSessions.fulfilled', () => {
    const store = configureStore({ reducer: { sessions: sessionsSlice } });
    const sessions = [{ id: '1', title: 'Test' }];
    
    store.dispatch(fetchSessions.fulfilled(sessions, '', undefined));
    
    expect(store.getState().sessions.sessions).toEqual(sessions);
  });
});
```

### 2. **Tests d'intégration**

```typescript
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '../store';

const renderWithRedux = (component: React.ReactElement) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};
```

## 🔒 Sécurité

### 1. **Chiffrement des données sensibles**

Réactivez le chiffrement en installant correctement `redux-persist-transform-encrypt` :

```typescript
import { encryptTransform } from 'redux-persist-transform-encrypt';

const encryptTransformConfig = encryptTransform({
  secretKey: import.meta.env.REACT_APP_ENCRYPT_KEY!,
  onError: (error) => console.error('Encryption error:', error),
});
```

### 2. **Validation des données**

```typescript
import { z } from 'zod';

const SessionSchema = z.object({
  id: z.string(),
  title: z.string().min(1),
  description: z.string(),
  // ... autres champs
});

// Valider avant de sauvegarder
const validateSession = (session: unknown): Session => {
  return SessionSchema.parse(session);
};
```

## 📊 Monitoring et Debug

### 1. **Redux DevTools**

Les DevTools sont déjà configurés en développement. Utilisez l'extension Redux DevTools pour Chrome/Firefox.

### 2. **Logging personnalisé**

```typescript
const loggerMiddleware: Middleware = (store) => (next) => (action) => {
  console.group(action.type);
  console.info('dispatching', action);
  const result = next(action);
  console.log('next state', store.getState());
  console.groupEnd();
  return result;
};
```

## 🚀 Déploiement

### 1. **Variables d'environnement**

Configurez les variables d'environnement pour chaque environnement :
- `.env.development`
- `.env.staging`  
- `.env.production`

### 2. **Build optimisé**

```bash
npm run build
```

### 3. **Service Worker**

Activez le service worker pour le cache et la synchronisation offline :

```typescript
// Dans src/index.tsx
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.register('/sw.js');
}
```

## 📚 Ressources utiles

- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [Redux Persist Documentation](https://github.com/rt2zz/redux-persist)
- [Firebase Documentation](https://firebase.google.com/docs)
- [React Redux Hooks](https://react-redux.js.org/api/hooks)

## 🐛 Dépannage

### Problèmes courants :

1. **Données non persistées** : Vérifiez que PersistGate entoure votre app
2. **Erreurs de type TypeScript** : Utilisez les hooks typés (`useAppDispatch`, `useAppSelector`)
3. **Synchronisation qui ne fonctionne pas** : Vérifiez la configuration Firebase et les règles Firestore
4. **Performance lente** : Utilisez des sélecteurs mémorisés avec `createSelector`

### Debug :

```typescript
// Activer les logs Redux
const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(loggerMiddleware),
});

// Inspecter l'état
console.log('Current state:', store.getState());
```

---

## 🎯 Prochaines étapes recommandées

1. **Configurez Firebase** avec vos vraies credentials
2. **Implémentez l'authentification** complète
3. **Testez la synchronisation** avec des données réelles
4. **Ajoutez la gestion des conflits**
5. **Implémentez le middleware de sync automatique**
6. **Ajoutez des tests** pour vos slices
7. **Optimisez les performances** avec des sélecteurs
8. **Configurez le monitoring** en production

L'infrastructure Redux est maintenant en place et fonctionnelle. Vous pouvez commencer à l'utiliser dans vos composants et l'étendre selon vos besoins spécifiques !
