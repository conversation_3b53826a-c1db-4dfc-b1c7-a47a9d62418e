// src/services/adsService.ts
import { UserProfileData } from '../store/slices/userProfileSlice';

export interface AdConfig {
  maxAdsPerDay: number;
  adFreeDurationMs: number; // Durée d'accès gratuit après une pub (en millisecondes)
  adProviders: {
    google: {
      enabled: boolean;
      adUnitId: string;
    };
    facebook: {
      enabled: boolean;
      placementId: string;
    };
  };
}

export interface ContentAccessLevel {
  free: string[];
  premium: string[];
  adSupported: string[];
}

// Correction ici pour Vite: Utilisez import.meta.env.MODE
const IS_DEVELOPMENT = import.meta.env.MODE === 'development';

// URL du script Google Publisher Tag (GPT)
const GOOGLE_GPT_SCRIPT_URL = 'https://securepubads.g.doubleclick.net/tag/js/gpt.js';

export class AdsService {
  private static readonly DEFAULT_CONFIG: AdConfig = {
    maxAdsPerDay: 30,
    adFreeDurationMs: 60 * 60 * 1000, // 1 heure
    adProviders: {
      google: {
        enabled: true,
        // Correction ici pour Vite: Utilisez import.meta.env.VITE_...
        adUnitId: import.meta.env.VITE_GOOGLE_AD_UNIT_ID || 'ca-app-pub-3940256099942544/**********', // Test ID
      },
      facebook: {
        enabled: false,
        // Correction ici pour Vite: Utilisez import.meta.env.VITE_...
        placementId: import.meta.env.VITE_FACEBOOK_PLACEMENT_ID || '',
      },
    },
  };

  private static readonly CONTENT_ACCESS: ContentAccessLevel = {
    free: [
      'meditation',
      'stories',
      'background_music'
    ],
    premium: [
      'ambient_sounds',
      'binaural_beats',
      'hypnosis',
      'nlp',
      'games',
      'advanced_sessions'
    ],
    adSupported: [
      'ambient_sounds',
      'binaural_beats',
      'hypnosis',
      'nlp',
      'games',
      'advanced_sessions'
    ]
  };

  static getConfig(): AdConfig {
    return this.DEFAULT_CONFIG;
  }

  /**
   * Vérifie si le script Google GPT est déjà chargé, sinon tente de l'injecter.
   * C'est une bonne pratique si vous ne voulez pas le charger statiquement dans index.html
   * ou si votre application est rendue côté serveur.
   * Pour la plupart des SPAs React, le chargement dans index.html est suffisant.
   */
  private static async ensureGoogleGptLoaded(): Promise<void> {
    if (typeof window === 'undefined' || (window as any).googletag) {
      return; // Déjà chargé ou pas dans un environnement de navigateur
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.async = true;
      script.src = GOOGLE_GPT_SCRIPT_URL;
      script.onload = () => {
        (window as any).googletag = (window as any).googletag || {cmd: []};
        resolve();
      };
      script.onerror = () => reject(new Error('Failed to load Google GPT script.'));
      document.head.appendChild(script);
    });
  }


  static canAccessContent(contentType: string, userProfile: UserProfileData | null): {
    canAccess: boolean;
    reason: 'free' | 'premium' | 'ad_free_period' | 'blocked';
    requiresAd: boolean;
  } {
    // Si pas de profil utilisateur, l'accès est limité au contenu gratuit
    if (!userProfile) {
      return {
        canAccess: this.CONTENT_ACCESS.free.includes(contentType),
        reason: this.CONTENT_ACCESS.free.includes(contentType) ? 'free' : 'blocked',
        requiresAd: false
      };
    }

    const { preferences } = userProfile;

    // Vérifier si l'utilisateur a un abonnement premium actif
    if (preferences.subscriptions?.active && preferences.subscriptions.tier === 'premium') {
      const now = new Date();
      const endsAt = preferences.subscriptions.endsAt;

      // S'assurer que endsAt est une date valide et qu'elle n'est pas passée
      if (endsAt && new Date(endsAt) > now) {
        return {
          canAccess: true,
          reason: 'premium',
          requiresAd: false
        };
      }
    }

    // Vérifier si le contenu est gratuit
    if (this.CONTENT_ACCESS.free.includes(contentType)) {
      return {
        canAccess: true,
        reason: 'free',
        requiresAd: false
      };
    }

    // Vérifier si l'utilisateur est dans la période sans pub après avoir regardé une pub
    const adFreeUntil = preferences.adWatchData?.adFreeUntil;
    if (adFreeUntil && new Date(adFreeUntil) > new Date()) {
      return {
        canAccess: true,
        reason: 'ad_free_period',
        requiresAd: false
      };
    }

    // Le contenu nécessite soit un abonnement premium soit de regarder une pub
    if (this.CONTENT_ACCESS.adSupported.includes(contentType)) {
      return {
        canAccess: false,
        reason: 'blocked',
        requiresAd: true
      };
    }

    // Contenu non listé ou non accessible
    return {
      canAccess: false,
      reason: 'blocked',
      requiresAd: false
    };
  }

  static canWatchAd(userProfile: UserProfileData): {
    canWatch: boolean;
    reason: string;
    adsWatchedToday: number;
    maxAds: number;
  } {
    const config = this.getConfig();
    const today = new Date().toISOString().substring(0, 10);
    const adWatchData = userProfile.preferences.adWatchData;

    let adsWatchedToday = 0;

    // Vérifier si le compteur est pour aujourd'hui
    if (adWatchData && adWatchData.lastAdWatchDay === today) {
      adsWatchedToday = adWatchData.adsWatchedToday || 0;
    } else {
      // Si c'est un nouveau jour ou pas de données, réinitialiser
      adsWatchedToday = 0;
    }

    const canWatch = adsWatchedToday < config.maxAdsPerDay;

    return {
      canWatch,
      reason: canWatch ? 'allowed' : 'daily_limit_reached',
      adsWatchedToday,
      maxAds: config.maxAdsPerDay
    };
  }

  static async showAd(): Promise<{
    success: boolean;
    error?: string;
    provider?: string;
  }> {
    const config = this.getConfig();

    // Simulation pour le développement en premier, pour faciliter les tests
    if (IS_DEVELOPMENT) { // Utilise la constante corrigée
      console.log('Running ad simulation in development mode.');
      return this.simulateAd();
    }

    // Essayer Google Ads en premier
    if (config.adProviders.google.enabled) {
      try {
        await this.ensureGoogleGptLoaded(); // S'assurer que le script GPT est chargé
        const result = await this.showGoogleAd(config.adProviders.google.adUnitId);
        if (result) {
          return {
            success: true,
            provider: 'google'
          };
        } else {
          console.warn('Google Ad did not render or was empty.');
        }
      } catch (error: any) {
        console.error('Google Ad failed:', error);
      }
    }

    // Fallback vers Facebook Ads
    if (config.adProviders.facebook.enabled) {
      try {
        const result = await this.showFacebookAd(config.adProviders.facebook.placementId);
        if (result) {
          return {
            success: true,
            provider: 'facebook'
          };
        } else {
          console.warn('Facebook Ad did not render or was empty.');
        }
      } catch (error: any) {
        console.error('Facebook Ad failed:', error);
      }
    }

    return {
      success: false,
      error: 'No ad providers available or all attempts failed.'
    };
  }

  private static async showGoogleAd(adUnitId: string): Promise<boolean> {
    if (typeof window === 'undefined' || !(window as any).googletag) {
      console.error('Google Ads SDK (googletag) not available.');
      return false; // Ou rejeter la promesse si vous voulez une gestion d'erreur plus stricte
    }

    return new Promise((resolve) => {
      const googletag = (window as any).googletag;

      googletag.cmd.push(() => {
        // Assurez-vous que l'élément 'ad-container' existe dans votre DOM là où vous voulez afficher la pub.
        // Vous devrez le créer dynamiquement ou l'avoir statiquement dans un composant.
        // Exemple: <div id="ad-container" style={{ width: '320px', height: '50px' }}></div>
        const adContainer = document.getElementById('ad-container');
        if (!adContainer) {
          console.error('Ad container element #ad-container not found.');
          return resolve(false);
        }
        // Nettoyer l'ancien contenu du container si nécessaire
        adContainer.innerHTML = '';


        const slot = googletag.defineSlot(adUnitId, [320, 50], 'ad-container'); // Taille d'exemple
        if (slot) {
          googletag.pubads().enableSingleRequest(); // Optimisation
          googletag.pubads().disableInitialLoad(); // Permet de charger la pub manuellement
          slot.addService(googletag.pubads());

          googletag.pubads().addEventListener('slotRenderEnded', (event: any) => {
            if (event.slot === slot) { // Assurez-vous que l'événement est pour notre slot
              resolve(!event.isEmpty);
            }
          });

          // googletag.enableServices(); // C'est généralement appelé une fois au démarrage de l'app
          googletag.display('ad-container'); // Charge et affiche la pub
        } else {
          resolve(false);
        }
      });
    });
  }

  private static async showFacebookAd(placementId: string): Promise<boolean> {
    // Implémentation Facebook Audience Network (pour mobile/React Native plutôt que web)
    // Pour le web, cela serait très spécifique au SDK Facebook JS.
    // L'exemple ci-dessous est un placeholder.
    if (typeof window === 'undefined' || !(window as any).FB) {
      console.error('Facebook Ads SDK (FB) not available.');
      return false;
    }

    return new Promise((resolve) => {
      // Logique pour afficher une publicité Facebook via leur SDK
      console.log(`Simulating Facebook Ad for placement: ${placementId}`);
      setTimeout(() => {
        resolve(true); // Simule le succès
      }, 1500);
    });
  }

  private static async simulateAd(): Promise<{
    success: boolean;
    error?: string;
    provider?: string;
  }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const success = Math.random() > 0.1; // 90% de chance de succès
        resolve({
          success,
          error: success ? undefined : 'Simulated ad failure',
          provider: 'simulation'
        });
      }, 2000); // Simuler 2 secondes de chargement
    });
  }

  static getAdFreeTimeRemaining(userProfile: UserProfileData): number {
    const adFreeUntil = userProfile.preferences.adWatchData?.adFreeUntil;
    if (!adFreeUntil) return 0;

    return Math.max(0, new Date(adFreeUntil).getTime() - Date.now());
  }

  static formatTimeRemaining(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }
}