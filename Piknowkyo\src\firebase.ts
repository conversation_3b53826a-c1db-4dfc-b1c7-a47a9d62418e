// src/firebase.ts

import { initializeApp } from "firebase/app";
import { getAuth, connectAuthEmulator } from "firebase/auth";
// Importez initializeFirestore, connectFirestoreEmulator, et CACHE_SIZE_UNLIMITED
import { initializeFirestore, connectFirestoreEmulator, CACHE_SIZE_UNLIMITED, persistentLocalCache } from "firebase/firestore";
import { getStorage, connectStorageEmulator } from "firebase/storage";
import { getFunctions, connectFunctionsEmulator } from "firebase/functions";

// Votre configuration Firebase
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

// Initialisation de l'application Firebase
export const app = initializeApp(firebaseConfig);

// Initialisation des services Firebase
export const auth = getAuth(app);

// --- CORRECTION CLÉ ICI : Configurez la persistance via initializeFirestore ---
export const db = initializeFirestore(app, {
  localCache: persistentLocalCache({ cacheSizeBytes: CACHE_SIZE_UNLIMITED })
});
// Note: Si vous vouliez un cache en mémoire (pas de persistance), vous mettriez:
// cache: { kind: "memory" },
// Ou pour le comportement par défaut de Firestore (persistance activée par défaut, 40MB):
// Laissez juste initializeFirestore(app, {}); sans l'option cache.

export const storage = getStorage(app);
export const functions = getFunctions(app); // Initialisez les fonctions Firebase

// Configuration des émulateurs en développement
// Utilisez import.meta.env.MODE pour la vérification de l'environnement
// Utilisez import.meta.env.VITE_USE_FIREBASE_EMULATOR pour votre variable d'environnement
if (import.meta.env.MODE === 'development' && import.meta.env.VITE_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectFirestoreEmulator(db, 'localhost', 8080);
    connectAuthEmulator(auth, 'http://localhost:9099'); // L'émulateur Auth utilise souvent http://
    connectStorageEmulator(storage, 'localhost', 9199);
    connectFunctionsEmulator(functions, 'localhost', 5001); // Port par défaut pour les fonctions
    console.log('Connected to Firebase emulators!');
  } catch (error) {
    // Si déjà connecté ou autre erreur d'initialisation
    console.warn('Firebase emulators connection warning:', error);
  }
}

// --- LIGNE SUPPRIMÉE : enableIndexedDbPersistence n'est plus nécessaire ---
// L'ancienne fonction enableIndexedDbPersistence(db).catch(...) doit être supprimée.
// Elle est remplacée par la configuration `cache` dans initializeFirestore.
/*
enableIndexedDbPersistence(db).catch((err) => {
  if (err.code === 'failed-precondition') {
    console.warn('Firebase persistence failed: Multiple tabs open');
  } else if (err.code === 'unimplemented') {
    console.warn('Firebase persistence not available in this browser');
  } else {
    console.error('Firebase persistence error:', err);
  }
});
*/

// Les exportations de variables sont déjà faites avec 'export const' au-dessus
// export { app, auth, db, storage, functions };