// src/store/slices/sessionsSlice.ts (VERSION CORRIGÉE)
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { collection, getDocs, addDoc } from 'firebase/firestore'; // Importez addDoc pour la création
import { db } from '../../firebase'; // Importez votre instance Firestore

// IMPORTEZ LA DÉFINITION DE SESSION DE VOTRE FICHIER MODELS CENTRAL
import { Session } from '../../models'; // <-- CECI EST CRUCIAL !

// SUPPRIMEZ TOUTE DÉFINITION LOCALE DE L'INTERFACE 'Session' QUI POUVAIT ÊTRE ICI.
// export type SessionCategory = 'meditation' | 'story' | 'exercise' | 'challenge' | 'other'; // Supprimé pour la dynamique
// export interface Session { ... } // Supprimé pour la dynamique

export interface SessionsState {
  sessions: Session[];
  currentSession: Session | null;
  loading: boolean; // <-- RENOMMÉ DE 'isLoading' À 'loading'
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: {
    created: Session[];
    updated: Session[];
    deleted: string[];
  };
}

const initialState: SessionsState = {
  sessions: [],
  currentSession: null,
  loading: false, // <-- Initialisation avec 'loading'
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

// Actions asynchrones
export const fetchSessions = createAsyncThunk(
  'sessions/fetchSessions',
  async (_, { rejectWithValue }) => {
    try {
      const sessionsCollection = collection(db, 'sessions');
      const querySnapshot = await getDocs(sessionsCollection);
      const sessions = querySnapshot.docs.map(doc => {
        const data = doc.data();
        // Assurez-vous que les données Firestore correspondent à votre interface Session
        // Et que les Timestamps sont convertis si nécessaire (Ex: si vous avez des dates)
        // Vous pouvez ajouter une fonction de conversion ici si vos dates Firebase sont des Timestamps
        // Ex: data.createdAt ? data.createdAt.toDate().toISOString() : null
        return {
          id: doc.id,
          // Ici, assurez-vous que toutes les propriétés attendues par l'interface `Session` sont présentes
          // ou que Firestore les fournit. Sinon, vous devrez ajouter des valeurs par défaut ou gérer l'optionalité.
          title: data.title || 'Untitled',
          description: data.description || '',
          audioUrl: data.audioUrl || '', // Important : doit exister
          duration: data.duration || 0,   // Important : doit exister
          type: data.type || 'meditation', // Dynamique, mais avec un fallback
          category: data.category || 'general', // Dynamique, mais avec un fallback
          language: data.language || 'fr',
          imageUrl: data.imageUrl || undefined,
          durationMinutes: data.durationMinutes || undefined,
          estimatedDuration: data.estimatedDuration || undefined,
          rating: data.rating || undefined,
          tags: data.tags || undefined,
          benefits: data.benefits || undefined,
          comments: data.comments || undefined,
          script: data.script || undefined,
          audio: data.audio || undefined,
        } as Session; // Castez pour assurer la conformité à l'interface
      });
      return sessions;
    } catch (error: any) {
      console.error("Error fetching sessions from Firestore:", error);
      return rejectWithValue(error.message || 'Failed to fetch sessions.');
    }
  }
);

export const createSession = createAsyncThunk(
  'sessions/createSession',
  // Utilisez l'interface Session importée de models.ts
  async (sessionData: Omit<Session, 'id'>, { rejectWithValue }) => {
    try {
      // Ajoutez la session à Firestore
      const docRef = await addDoc(collection(db, 'sessions'), sessionData);
      const newSession: Session = {
        id: docRef.id, // L'ID généré par Firestore
        ...sessionData,
      };

      // Si offline, ajouter aux changements en attente, sinon pas besoin
      // Cette logique peut aussi être gérée par votre service de synchronisation si vous avez un système robuste.
      // Pour l'instant, on retourne la session créée.
      return newSession;
    } catch (error: any) {
      console.error("Error creating session in Firestore:", error);
      return rejectWithValue(error.message || 'Failed to create session.');
    }
  }
);

const sessionsSlice = createSlice({
  name: 'sessions',
  initialState,
  reducers: {
    setCurrentSession: (state, action: PayloadAction<Session | null>) => {
      state.currentSession = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
    // Vous pouvez ajouter des reducers pour gérer les sessions localement avant/après sync
    // par exemple, addSessionOffline (pour optimiste), removeSessionOffline, etc.
  },
  extraReducers: (builder) => {
    builder
      // Fetch Sessions
      .addCase(fetchSessions.pending, (state) => {
        state.loading = true; // <-- UTILISEZ 'loading'
        state.error = null;
      })
      .addCase(fetchSessions.fulfilled, (state, action: PayloadAction<Session[]>) => {
        state.loading = false; // <-- UTILISEZ 'loading'
        state.sessions = action.payload;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchSessions.rejected, (state, action) => {
        state.loading = false; // <-- UTILISEZ 'loading'
        state.error = action.payload as string;
      })
      // Create Session
      .addCase(createSession.pending, (state) => {
        state.loading = true; // <-- UTILISEZ 'loading'
        state.error = null;
      })
      .addCase(createSession.fulfilled, (state, action: PayloadAction<Session>) => {
        state.loading = false; // <-- UTILISEZ 'loading'
        state.sessions.push(action.payload);
        // La logique des pendingChanges pourrait être gérée par le service de sync global
        // mais si elle reste ici, c'est pour un mode offline-first avant la sync réelle.
        // if (!navigator.onLine) {
           state.pendingChanges.created.push(action.payload);
        // }
      })
      .addCase(createSession.rejected, (state, action) => {
        state.loading = false; // <-- UTILISEZ 'loading'
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentSession,
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = sessionsSlice.actions;

export default sessionsSlice.reducer;