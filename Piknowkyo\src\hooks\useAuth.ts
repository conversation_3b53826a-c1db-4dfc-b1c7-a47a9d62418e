// src/hooks/useAuth.ts
import { useEffect, useState } from 'react';
import { User, onAuthStateChanged } from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs, Firestore } from 'firebase/firestore';
import { auth, db } from '../firebase';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Assurez-vous que cette fonction est exportée si vous l'utilisez ailleurs
export async function generateAnonymousPseudo(uid: string, t: TFunction, db: Firestore): Promise<string> {
  const adjectivesObj = t('pseudoGenerator.adjectives', { returnObjects: true }) as Record<string, string>;
  const nounsObj = t('pseudoGenerator.nouns', { returnObjects: true }) as Record<string, string>;

  const adjectives = Object.values(adjectivesObj);
  const nouns = Object.values(nounsObj);

  if (adjectives.length === 0 || nouns.length === 0) {
    console.warn("Translation keys for pseudo generator (adjectives/nouns) are empty or incorrect. Using fallback.");
    return `User_${uid.substring(0, 4)}_${Math.random().toString(16).substring(2, 6).toUpperCase()}`;
  }

  const MAX_RETRIES = 10;
  for (let i = 0; i < MAX_RETRIES; i++) {
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomHexSuffix = Math.random().toString(16).substring(2, 6).toUpperCase();

    const potentialPseudo = `${randomAdjective} ${randomNoun} ${randomHexSuffix}`;

    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('publicName', '==', potentialPseudo));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return potentialPseudo;
    }
  }

  console.warn("Could not generate a unique pseudo after multiple retries. Using ultimate fallback.");
  return `FallbackUser_${uid.substring(0, 4)}_${Date.now().toString(16).substring(0, 4).toUpperCase()}`;
}


export const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { t } = useTranslation();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setLoading(true); // Set loading to true at the start of auth state processing

      if (firebaseUser) {
        const userDocRef = doc(db, 'users', firebaseUser.uid);
        let userDocSnap;
        let attempts = 0;
        const maxAttempts = 3;
        const retryDelay = 500; // ms

        while (attempts < maxAttempts) {
          try {
            userDocSnap = await getDoc(userDocRef);
            if (userDocSnap.exists() || attempts === maxAttempts - 1) { // If found, or last attempt
              break;
            }
          } catch (error) {
            console.warn(`useAuth: Attempt ${attempts + 1} to getDoc failed`, error);
            if (attempts === maxAttempts - 1) {
              // Handle final attempt failure if necessary, though often "Null value error" isn't caught here
            }
          }

          if (!userDocSnap || !userDocSnap.exists()) {
               console.log(`useAuth: User doc not found on attempt ${attempts + 1}. Retrying after ${retryDelay}ms...`);
               await delay(retryDelay);
          }
          attempts++;
        }

        if (!userDocSnap) {
            console.error('useAuth: Failed to get user document snapshot after multiple attempts.');
            setUser(null);
            setLoading(false);
            return;
        }

        if (!userDocSnap.exists()) {
          console.log("useAuth: Creating new user document in Firestore for:", firebaseUser.uid);
          const initialPublicName = await generateAnonymousPseudo(firebaseUser.uid, t, db);
          const isoDate = new Date().toISOString();
          await setDoc(userDocRef, {
            email: firebaseUser.email,
            createdAt: isoDate,
            lastLoginAt: isoDate,
            publicName: initialPublicName,
            preferences: {
              theme: 'light',
              language: 'fr',
              notificationsEnabled: true,
              // preferedGender: 'masculine', // Ensure this field is part of UserProfileData if used
              audioConfig: {
                enableMusic: false, music: { volume: 0.5, url: '' },
                enableAmbient: false, ambient: { volume: 0.3, url: '' },
                enableBinaural: false, binaural: { volume: 0.2, baseFreq: 100, beatFreq: 10 },
                voice: { volume: 1, language: 'fr', provider: 'browser', voice: 'auto' },
              },
              unlockedFeatures: [],
              subscriptions: {
                active: false,
                startDate: null,
                endsAt: null,
                cancelAtPeriodEnd: false,
                tier: 'free',
              },
              adWatchData: {
                lastAdWatchedAt: null,
                adFreeUntil: null,
                adsWatchedToday: 0,
                lastAdWatchDay: null,
              },
              stats: {
                totalSessionsPlayed: 0,
                totalMinutesPlayed: 0,
                lastSessionPlayedAt: null,
                currentStreak: 0,
                longestStreak: 0,
                totalNotesWritten: 0,
                totalSessionsWithNotes: 0,
                favoriteSessionId: [],
              },
            },
          });
        } else {
            const updateData: any = {
                lastLoginAt: new Date().toISOString()
            };
            const userData = userDocSnap.data();

            if (!userData.publicName) {
                const newPublicName = await generateAnonymousPseudo(firebaseUser.uid, t, db);
                updateData.publicName = newPublicName;
            }
            if (!userData.preferences || !userData.preferences.subscriptions) {
                updateData['preferences.subscriptions'] = { active: false, startDate: null, endsAt: null, cancelAtPeriodEnd: false, tier: 'free' };
            }
            if (!userData.preferences || !userData.preferences.adWatchData) {
                updateData['preferences.adWatchData'] = { lastAdWatchedAt: null, adFreeUntil: null, adsWatchedToday: 0, lastAdWatchDay: null };
            }
            if (!userData.preferences || !userData.preferences.audioConfig) {
                updateData['preferences.audioConfig'] = {
                    enableMusic: false, music: { volume: 0.5, url: '' },
                    enableAmbient: false, ambient: { volume: 0.3, url: '' },
                    enableBinaural: false, binaural: { volume: 0.2, baseFreq: 100, beatFreq: 10 },
                    voice: { volume: 1, language: 'fr', provider: 'browser', voice: 'auto' },
                };
            } else {
                // Standardize existing audio volumes if they are not in the 0-1 scale
                const audioCfg = userData.preferences.audioConfig;
                const newAudioCfg = { ...audioCfg };
                let audioCfgChanged = false;

                if (audioCfg.music && typeof audioCfg.music.volume === 'number' && audioCfg.music.volume > 1) {
                    newAudioCfg.music = { ...audioCfg.music, volume: audioCfg.music.volume / 100 };
                    audioCfgChanged = true;
                }
                if (audioCfg.ambient && typeof audioCfg.ambient.volume === 'number' && audioCfg.ambient.volume > 1) {
                    newAudioCfg.ambient = { ...audioCfg.ambient, volume: audioCfg.ambient.volume / 100 };
                    audioCfgChanged = true;
                }
                if (audioCfg.binaural && typeof audioCfg.binaural.volume === 'number' && audioCfg.binaural.volume > 1) {
                    newAudioCfg.binaural = { ...audioCfg.binaural, volume: audioCfg.binaural.volume / 100 };
                    audioCfgChanged = true;
                }
                if (audioCfg.voice && typeof audioCfg.voice.volume === 'number' && audioCfg.voice.volume > 1) {
                    newAudioCfg.voice = { ...audioCfg.voice, volume: audioCfg.voice.volume / 100 };
                    audioCfgChanged = true;
                }
                if(audioCfgChanged) updateData['preferences.audioConfig'] = newAudioCfg;
            }

            if (Object.keys(updateData).length > 1 || (Object.keys(updateData).length === 1 && !updateData.hasOwnProperty('lastLoginAt'))) { // Check if more than just lastLoginAt is being updated
                 await updateDoc(userDocRef, updateData);
            } else { // Only update lastLoginAt if no other changes
                await updateDoc(userDocRef, { lastLoginAt: updateData.lastLoginAt });
            }
        }
        setUser(firebaseUser); // Set user after all operations
      } else { // firebaseUser is null
        setUser(null);
      }
      setLoading(false); // Set loading to false at the end
    });

    return () => unsubscribe();
  }, [t]); // t should be stable, but included as per eslint rules if it complains

  return { user, loading }; // Return user and loading state
};