// src/components/ReduxExample.tsx (VERSION COMPLÈTE ET CORRIGÉE)
import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
// Importez les hooks nécessaires
import { useAppDispatch, useSessions, useSync, useOfflineStatusHook, usePricing } from '../store/hooks';
import { fetchSessions, createSession, setCurrentSession } from '../store/slices/sessionsSlice';
import { performFullSync } from '../store/slices/syncSlice';
// IMPORTEZ LA DÉFINITION DE SESSION DE VOTRE FICHIER MODELS CENTRAL
import { Session } from '../models'; // <-- ASSUREZ-VOUS QUE CE CHEMIN EST CORRECT ET QU'IL EST LE SEUL POUR SESSION

// Importez les actions et les interfaces de prix depuis le slice pricing
import { fetchPricingConfig, PricingPlan } from '../store/slices/pricingSlice';
import { PaymentService } from '../services/paymentService'; // Pour utiliser le service de paiement

const Container = styled.div`
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;
  color: #333;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
`;

const Title = styled.h2`
  margin: 0;
  color: #333;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' | 'success' }>`
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  white-space: nowrap;

  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: #007bff;
          color: white;
          &:hover { background: #0056b3; }
        `;
      case 'success':
        return `
          background: #28a745;
          color: white;
          &:hover { background: #1e7e34; }
        `;
      default:
        return `
          background: #6c757d;
          color: white;
          &:hover { background: #545b62; }
        `;
    }
  }}

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

const StatusBar = styled.div`
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
  font-size: 0.9rem;
`;

const StatusItem = styled.div`
  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const SessionsList = styled.div`
  display: grid;
  gap: 12px;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
`;

const SessionCard = styled.div<{ $isSelected?: boolean }>`
  background: white;
  border: 2px solid ${props => props.$isSelected ? '#007bff' : '#dee2e6'};
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);

  &:hover {
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;

const SessionTitle = styled.h3`
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.1rem;
`;

const SessionDescription = styled.p`
  margin: 0 0 12px 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
`;

const SessionMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #888;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-size: 1rem;
  color: #666;
`;

const ErrorMessage = styled.div`
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
`;

const OfflineIndicator = styled.div`
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 20px;
  text-align: center;
`;

const PriceDisplay = styled.div`
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 6px;
  padding: 15px;
  margin-top: 20px;
  margin-bottom: 20px;
  font-size: 1.1rem;
  text-align: center;
`;

const PricingPlanCard = styled(SessionCard)`
  cursor: default;
  &:hover {
    border-color: #dee2e6;
    transform: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  }
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  ul {
    list-style: none;
    padding: 0;
    margin: 12px 0;
    li {
      margin-bottom: 5px;
      font-size: 0.9rem;
      color: #555;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
`;


const ReduxExample: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  // Correction: destructure 'loading' (pas 'isLoading')
  const { sessions, currentSession, loading, error } = useSessions();
  const { isSyncing, syncStatus, syncErrors } = useSync();
  // Correction ici : utilisez le hook `useOfflineStatusHook`
  const { isOffline, hasPendingChanges } = useOfflineStatusHook();
  // Correction ici : utilisez le hook `usePricing` pour les données de prix
  const { pricingPlans, isLoading: pricingLoading, error: pricingError } = usePricing();

  const [sessionCounter, setSessionCounter] = useState(1);
  const [paymentProcessing, setPaymentProcessing] = useState(false);

  useEffect(() => {
    // Les thunks de Redux Toolkit n'ont généralement pas besoin de 'as any'
    // si leurs types de retour et d'arguments sont correctement définis.
    dispatch(fetchSessions());
    dispatch(fetchPricingConfig());
  }, [dispatch]);

  const handleCreateSession = async () => {
    // Correction: Assurez-vous que newSessionData inclut toutes les propriétés requises
    // par l'interface `Session` de `src/models.ts`.
    // J'ai ajouté des valeurs par défaut pour `audioUrl` et `duration`.
    const newSession: Omit<Session, 'id'> = {
      title: `Session Redux ${sessionCounter}`,
      description: `Session créée via Redux le ${new Date().toLocaleString()}`,
      type: 'meditation', // Assurez-vous que votre DB/JSON contient des types dynamiques pour la cohérence
      category: 'relaxation', // Assurez-vous que votre DB/JSON contient des catégories dynamiques
      language: 'fr',
      audioUrl: 'https://example.com/default_audio.mp3', // Exemple, à adapter
      duration: Math.floor(Math.random() * 1800) + 300, // Durée en secondes (5-35 minutes)
      estimatedDuration: Math.floor(Math.random() * 30) + 5, // Durée estimée en minutes pour l'affichage
      // durationMinutes: Math.floor(Math.random() * 30) + 5, // Si vous utilisez aussi durationMinutes
      tags: ['redux', 'test', 'demo'],
      benefits: ['Démonstration Redux', 'Test de persistance'],
      // Ajoutez d'autres champs obligatoires ou pertinents selon votre `Session` interface dans `models.ts`
    };

    try {
      await dispatch(createSession(newSession)).unwrap();
      setSessionCounter(prev => prev + 1);
    } catch (error) {
      console.error('Erreur lors de la création de la session:', error);
      alert(`Erreur lors de la création de la session: ${(error as Error).message}`);
    }
  };

  const handleSessionClick = (session: Session) => {
    dispatch(setCurrentSession(session));
  };

  const handleSync = () => {
    dispatch(performFullSync());
  };

  const handleRefresh = () => {
    dispatch(fetchSessions());
    dispatch(fetchPricingConfig());
  };

  const handleCheckout = async (planId: string) => {
    if (!pricingPlans.length) {
      alert("Prix non chargés, impossible de procéder au paiement.");
      return;
    }
    setPaymentProcessing(true);
    try {
      // Remplacez par le vrai user.uid depuis votre hook d'authentification
      const dummyUserId = "user_test_123_dynamic_example";
      const result = await PaymentService.createCheckoutSession(planId, dummyUserId, pricingPlans);

      if (result.success && result.checkoutUrl) {
        window.location.href = result.checkoutUrl;
      } else {
        alert(`Échec de la création de la session de paiement: ${result.error || 'Erreur inconnue'}`);
      }
    } catch (err: any) {
      console.error("Erreur lors du checkout:", err);
      alert(`Une erreur inattendue est survenue lors du paiement: ${(err as Error).message}`);
    } finally {
      setPaymentProcessing(false);
    }
  };


  // Utilisez 'loading' ici au lieu de 'isLoading'
  if (loading && sessions.length === 0) {
    return (
      <Container>
        <LoadingSpinner>{t('reduxExample.loading')}</LoadingSpinner>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <Title>{t('reduxExample.title')}</Title>
        <ButtonGroup>
          <Button
            $variant="primary"
            onClick={handleCreateSession}
            disabled={loading} // Utilisez 'loading'
          >
            {loading ? 'Création...' : 'Créer Session'} {/* Utilisez 'loading' */}
          </Button>
          <Button onClick={handleRefresh} disabled={loading || pricingLoading}> {/* Utilisez 'loading' */}
            Actualiser
          </Button>
          <Button
            $variant="success"
            onClick={handleSync}
            disabled={!navigator.onLine || isSyncing}
          >
            {isSyncing ? 'Sync...' : 'Synchroniser'}
          </Button>
        </ButtonGroup>
      </Header>

      {isOffline && (
        <OfflineIndicator>
          🔌 Mode hors ligne - Les modifications seront synchronisées lors de la reconnexion
          {hasPendingChanges && ' (Changements en attente)'}
        </OfflineIndicator>
      )}

      <StatusBar>
        <StatusItem><strong>{t('reduxExample.reduxState')}</strong></StatusItem>
        <StatusItem>• Sessions chargées : {sessions.length}</StatusItem>
        <StatusItem>• Session sélectionnée : {currentSession?.title || 'Aucune'}</StatusItem>
        <StatusItem>• Statut réseau : {isOffline ? '🔴 Hors ligne' : '🟢 En ligne'}</StatusItem>
        <StatusItem>• Synchronisation : {isSyncing ? '🔄 En cours' : syncStatus === 'success' ? '✅ OK' : syncStatus === 'error' ? '❌ Erreur' : '⏸️ Idle'}</StatusItem>
        <StatusItem>• Changements en attente : {hasPendingChanges ? '⚠️ Oui' : '✅ Non'}</StatusItem>
        <StatusItem>• Prix chargés : {pricingLoading ? 'Chargement...' : pricingPlans.length > 0 ? `✅ ${pricingPlans.length} plans` : '❌ Aucun'}</StatusItem>
      </StatusBar>

      {error && (
        <ErrorMessage>
          ❌ Erreur sessions: {error}
        </ErrorMessage>
      )}
      {pricingError && (
        <ErrorMessage>
          ❌ Erreur prix: {pricingError}
        </ErrorMessage>
      )}

      {syncErrors.length > 0 && (
        <ErrorMessage>
          ❌ Erreurs de synchronisation: {syncErrors.join(', ')}
        </ErrorMessage>
      )}

      <h3>Plans de Tarification ({pricingLoading ? 'Chargement...' : 'Chargés'})</h3>
      {pricingLoading && <LoadingSpinner>Chargement des plans de prix...</LoadingSpinner>}
      {pricingError && <ErrorMessage>Impossible de charger les plans de prix.</ErrorMessage>}
      {!pricingLoading && !pricingError && pricingPlans.length === 0 && (
        <PriceDisplay>Aucun plan de tarification disponible.</PriceDisplay>
      )}
      <SessionsList>
        {pricingPlans.map((plan) => (
          <PricingPlanCard key={plan.id}>
            <SessionTitle>{plan.name}</SessionTitle>
            <SessionDescription>
              {PaymentService.formatPrice(plan.price, plan.currency)} / {plan.interval === 'month' ? 'mois' : 'an'}
            </SessionDescription>
            <ul>
              {plan.features.map((feature, index) => (
                <li key={index}>{feature}</li>
              ))}
            </ul>
            <Button
              $variant="primary"
              onClick={() => handleCheckout(plan.id)}
              disabled={paymentProcessing}
              style={{ marginTop: '10px' }}
            >
              {paymentProcessing ? 'Redirection...' : `S'abonner (${plan.name})`}
            </Button>
          </PricingPlanCard>
        ))}
      </SessionsList>

      <h3 style={{ marginTop: '30px' }}>Sessions Disponibles</h3>
      <SessionsList>
        {sessions.map((session) => (
          <SessionCard
            key={session.id}
            $isSelected={currentSession?.id === session.id}
            onClick={() => handleSessionClick(session)}
          >
            <SessionTitle>{session.title}</SessionTitle>
            <SessionDescription>{session.description}</SessionDescription>
            <SessionMeta>
              {/* Ces propriétés devraient maintenant correspondre à l'interface Session de models.ts */}
              <span>{session.type} • {session.category}</span>
              <span>{session.estimatedDuration || session.durationMinutes || 'N/A'} min</span>
            </SessionMeta>
          </SessionCard>
        ))}
      </SessionsList>

      {sessions.length === 0 && !loading && ( // Utilisez 'loading'
        <LoadingSpinner>
          Aucune session disponible. Créez votre première session !
        </LoadingSpinner>
      )}
    </Container>
  );
};

export default ReduxExample;