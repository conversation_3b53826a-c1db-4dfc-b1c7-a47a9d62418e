import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>der, FiAlertCircle } from 'react-icons/fi'; // Ajout d'icônes de chargement/erreur

import { useAppDispatch, useAppSelector } from '../store/hooks'; // Importer les hooks Redux
import { useAuth } from '../hooks/useAuth'; // Importer useAuth pour l'UID de l'utilisateur
import { fetchHistory, HistoryEntry } from '../store/slices/historySlice'; // Importer le thunk et le type

// --- Styled Components (réutilisés et quelques ajouts) ---
const Container = styled.div`
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    font-size: 2.5rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }

  p {
    font-size: 1.1rem;
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.6;
  }
`;

const HistoryList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 1rem; /* Espace entre les éléments de la liste */
`;

const HistoryItem = styled.li`
  background: ${({ theme }) => theme.surface};
  border-radius: 12px;
  box-shadow: ${({ theme }) => theme.shadowSmall};
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${({ theme }) => theme.cardShadow}; /* Légèrement plus prononcé au survol */
  }

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .item-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: ${({ theme }) => theme.primary};
  }

  .item-type {
    background-color: ${({ theme }) => theme.accent}1A; /* Accent avec opacité */
    color: ${({ theme }) => theme.accent};
    padding: 0.3em 0.8em;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
  }

  .item-details {
    color: ${({ theme }) => theme.textSecondary};
    font-size: 0.95rem;
  }

  .item-timestamp {
    font-size: 0.8rem;
    color: ${({ theme }) => theme.textMuted};
    margin-top: 0.5rem;
    align-self: flex-end; /* Aligner la date à droite */
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem;
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  border: 2px dashed ${({ theme }) => theme.border};
  color: ${({ theme }) => theme.textSecondary};
  font-style: italic;
  font-size: 1.1rem;
`;

const LoadingMessage = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.textSecondary};
  gap: 10px;

  svg {
    animation: spin 1.5s linear infinite;
  }
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.errorColor};
  background-color: ${({ theme }) => theme.errorColor}1A;
  border: 1px solid ${({ theme }) => theme.errorColor};
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem auto;
  max-width: 500px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;


const HistoryPage: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { user } = useAuth(); // Obtenez l'utilisateur authentifié
  const { entries, loading, error } = useAppSelector(state => state.history); // Sélectionnez l'état de la slice

  // Charge l'historique quand l'utilisateur est disponible
  useEffect(() => {
    if (user?.uid) { // Assurez-vous que l'UID est disponible
      dispatch(fetchHistory(user.uid));
    }
  }, [user, dispatch]); // Déclenche le chargement si l'utilisateur change ou au montage

  const formatDuration = (durationInSeconds?: number): string => {
    if (durationInSeconds === undefined) return '';
    const minutes = Math.floor(durationInSeconds / 60);
    const seconds = durationInSeconds % 60;
    return `${minutes}${t('history.entry_minutes_short')} ${seconds > 0 ? `${seconds}${t('history.entry_seconds_short')}` : ''}`.trim();
  };

  const getTypeName = (type: HistoryEntry['type']): string => {
    switch (type) {
      case 'session': return t('history.entry_type_session');
      case 'game': return t('history.entry_type_game');
      case 'meditation': return t('history.entry_type_meditation');
      case 'exercise': return t('history.entry_type_exercise');
      case 'custom': return t('history.entry_type_custom');
      default: return type; // Fallback au cas où un nouveau type non traduit apparaît
    }
  };

  if (loading) {
    return (
      <Container>
        <Header>
          <h1><FiClock />{t('history.title')}</h1>
          <p>{t('history.description')}</p>
        </Header>
        <LoadingMessage><FiLoader /> {t('history.loading_entries')}</LoadingMessage>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <Header>
          <h1><FiClock />{t('history.title')}</h1>
          <p>{t('history.description')}</p>
        </Header>
        <ErrorMessage><FiAlertCircle /> {t('history.error_loading_entries')}: {error}</ErrorMessage>
      </Container>
    );
  }

  return (
    <Container>
      <Header>
        <h1>
          <FiClock />
          {t('history.title')}
        </h1>
        <p>{t('history.description')}</p>
      </Header>

      {entries.length === 0 ? (
        <EmptyState>
          {t('history.no_entries')}
        </EmptyState>
      ) : (
        <HistoryList>
          {entries.map((entry) => (
            <HistoryItem key={entry.id}>
              <div className="item-header">
                <span className="item-title">{entry.name}</span>
                <span className="item-type">{getTypeName(entry.type)}</span>
              </div>
              {entry.duration && (
                <span className="item-details">
                  {t('history.entry_duration', { duration: formatDuration(entry.duration) })}
                </span>
              )}
              {/* Vous pouvez afficher d'autres détails ici si `entry.details` contient quelque chose */}
              {entry.details && Object.keys(entry.details).length > 0 && (
                <span className="item-details">
                  {t('history.entry_details', { details: JSON.stringify(entry.details) })}
                </span>
              )}
              <span className="item-timestamp">
                {new Date(entry.timestamp).toLocaleDateString()} {new Date(entry.timestamp).toLocaleTimeString()}
              </span>
            </HistoryItem>
          ))}
        </HistoryList>
      )}
    </Container>
  );
};

export default HistoryPage;