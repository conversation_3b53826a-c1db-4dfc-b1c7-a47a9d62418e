// src/store/slices/authSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User as FirebaseUser } from 'firebase/auth'; // Importe le type User de Firebase pour le typage

// --- INTERFACE CLÉ : Version sérialisable de l'utilisateur Firebase ---
// C'est la structure SIMPLE et sérialisable que votre store Redux va stocker.
export interface SerializableUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  // IMPORTANT : N'ajoutez ici QUE des propriétés primitives ou des objets/tableaux simples.
  // PAS de fonctions, de classes (comme Firebase User object), de promesses, etc.
}

export interface AuthState {
  user: SerializableUser | null; // <-- Le store stocke cette version sérialisable
  isAuthenticated: boolean;
  isLoading: boolean; // État de chargement de l'authentification (ex: en train de vérifier la session)
  error: string | null;
  lastSyncTimestamp: number | null; // Pour le suivi de la dernière synchronisation
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Défini à true au démarrage car Firebase vérifie la session utilisateur
  error: null,
  lastSyncTimestamp: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // --- ACTION CORRIGÉE : Convertit l'objet Firebase User en SerializableUser ---
    setAuthUser: (state, action: PayloadAction<FirebaseUser | null>) => {
      if (action.payload) {
        // --- C'EST LA PARTIE CRUCIALE : Crée un nouvel objet simple et sérialisable ---
        state.user = {
          uid: action.payload.uid,
          email: action.payload.email,
          displayName: action.payload.displayName,
          photoURL: action.payload.photoURL,
          emailVerified: action.payload.emailVerified,
          // Si vous avez besoin d'autres champs de l'objet FirebaseUser (ex: phoneNumber, metadata),
          // ajoutez-les ici un par un, en vous assurant qu'ils sont sérialisables.
          // Exemple: phoneNumber: action.payload.phoneNumber || null,
        };
        state.isAuthenticated = true;
      } else {
        // Si aucun utilisateur Firebase, l'état utilisateur est nul
        state.user = null;
        state.isAuthenticated = false;
      }
      state.isLoading = false; // Le chargement initial de l'authentification est terminé
      state.error = null; // Réinitialise l'erreur en cas de succès de connexion/déconnexion
    },
    // --- NOUVELLES ACTIONS POUR LA GESTION DE L'ÉTAT DE CHARGEMENT ET D'ERREUR ---
    setAuthLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setAuthError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false; // L'erreur signifie que le chargement est terminé
    },
    // --- ACTION POUR NETTOYER L'ÉTAT D'AUTH (utile pour la déconnexion) ---
    clearAuth: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.isLoading = false; // Plus en chargement après nettoyage
      state.error = null;
      state.lastSyncTimestamp = null;
    },
    // Action existante pour la date de dernière synchronisation
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
  },
});

// Exportez toutes les actions
export const { setAuthUser, setAuthLoading, setAuthError, clearAuth, updateLastSyncTimestamp } = authSlice.actions;

// Exportez le reducer par défaut
export default authSlice.reducer;