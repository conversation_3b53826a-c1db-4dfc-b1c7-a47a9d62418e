// src/components/AccessStatusIndicator.tsx
import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { FiClock, FiStar, FiGift, FiLock } from 'react-icons/fi';
import { useAppSelector } from '../store/hooks';
import { AdsService } from '../services/adsService';

const StatusContainer = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 8px;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
  border: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
`;

const StatusIcon = styled.div<{ $status: 'premium' | 'ad_free' | 'free' | 'blocked' }>`
  color: ${({ theme, $status }) => {
    switch ($status) {
      case 'premium': return theme.primary;
      case 'ad_free': return theme.accent;
      case 'free': return theme.success;
      case 'blocked': return theme.errorColor;
      default: return theme.textSecondary;
    }
  }};
  font-size: 1.1rem;
`;

const StatusText = styled.span`
  color: ${({ theme }) => theme.text};
  flex: 1;
`;

const TimeRemaining = styled.span`
  color: ${({ theme }) => theme.accent};
  font-weight: 500;
  font-size: 0.85rem;
`;

interface AccessStatusIndicatorProps {
  contentType?: string;
  showTimeRemaining?: boolean;
}

const AccessStatusIndicator: React.FC<AccessStatusIndicatorProps> = ({ 
  contentType = 'meditation',
  showTimeRemaining = true 
}) => {
  const { t } = useTranslation();
  const { profile: userProfile } = useAppSelector(state => state.userProfile);
  const [timeRemaining, setTimeRemaining] = useState(0);

  useEffect(() => {
    if (!userProfile || !showTimeRemaining) return;

    const updateTimeRemaining = () => {
      const remaining = AdsService.getAdFreeTimeRemaining(userProfile);
      setTimeRemaining(remaining);
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 1000);

    return () => clearInterval(interval);
  }, [userProfile, showTimeRemaining]);

  if (!userProfile) {
    return (
      <StatusContainer>
        <StatusIcon $status="free">
          <FiLock />
        </StatusIcon>
        <StatusText>{t('access.login_required')}</StatusText>
      </StatusContainer>
    );
  }

  const accessResult = AdsService.canAccessContent(contentType, userProfile);
  
  const getStatusInfo = () => {
    switch (accessResult.reason) {
      case 'premium':
        return {
          icon: <FiStar />,
          status: 'premium' as const,
          text: t('access.premium_access'),
          showTime: false
        };
      case 'ad_free_period':
        return {
          icon: <FiGift />,
          status: 'ad_free' as const,
          text: t('access.ad_free_access'),
          showTime: true
        };
      case 'free':
        return {
          icon: <FiGift />,
          status: 'free' as const,
          text: t('access.free_content'),
          showTime: false
        };
      case 'blocked':
        return {
          icon: <FiLock />,
          status: 'blocked' as const,
          text: accessResult.requiresAd 
            ? t('access.premium_or_ad_required')
            : t('access.premium_required'),
          showTime: false
        };
      default:
        return {
          icon: <FiLock />,
          status: 'blocked' as const,
          text: t('access.access_denied'),
          showTime: false
        };
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <StatusContainer>
      <StatusIcon $status={statusInfo.status}>
        {statusInfo.icon}
      </StatusIcon>
      <StatusText>{statusInfo.text}</StatusText>
      {statusInfo.showTime && timeRemaining > 0 && showTimeRemaining && (
        <TimeRemaining>
          <FiClock style={{ marginRight: '0.25rem' }} />
          {AdsService.formatTimeRemaining(timeRemaining)}
        </TimeRemaining>
      )}
    </StatusContainer>
  );
};

export default AccessStatusIndicator;
