// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Permet la lecture et l'écriture à tout le monde
    // TRÈS IMPORTANT: NE PAS UTILISER EN PRODUCTION !
    match /{allPaths=**} {
      allow read, write: if true;
    }

    // Une fois que tout fonctionne, vous voudrez des règles plus strictes, par exemple:
    // match /profilePictures/{userId}/{fileName} {
    //   allow read;
    //   allow write: if request.auth.uid == userId;
    // }
    // match /publicAssets/{assetId} {
    //   allow read: if true;
    //   allow write: if request.auth.token.isAdmin == true;
    // }
  }
}