// src/services/paymentService.ts
import { httpsCallable, getFunctions } from 'firebase/functions';
import { app, functions } from '../firebase';
// Importez vos interfaces de prix du nouveau slice
import { PricingPlan } from '../store/slices/pricingSlice'; // <-- Modifié

// Vous pouvez conserver ces interfaces ici ou les déplacer vers un fichier partagé si elles sont utilisées ailleurs
// export interface PricingPlan { ... } // Déjà défini dans pricingSlice.ts
export interface PaymentProvider {
  name: 'stripe' | 'paddle';
  enabled: boolean;
  publicKey: string;
}

export interface SubscriptionStatus {
  active: boolean;
  tier: 'free' | 'premium';
  startDate: string | null;
  endsAt: string | null;
  cancelAtPeriodEnd: boolean;
  provider?: 'stripe' | 'paddle';
  subscriptionId?: string;
}

// Correction ici pour Vite: Utilisez import.meta.env.MODE
const IS_DEVELOPMENT = import.meta.env.MODE === 'development';

export class PaymentService {
  // Supprimez le tableau PRICING_PLANS hardcodé
  // private static readonly PRICING_PLANS: PricingPlan[] = [...];

  private static readonly PAYMENT_PROVIDERS: PaymentProvider[] = [
    {
      name: 'stripe',
      // Correction ici pour Vite: Utilisez import.meta.env.VITE_...
      enabled: !!import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY,
      publicKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
    },
    {
      name: 'paddle',
      // Correction ici pour Vite: Utilisez import.meta.env.VITE_...
      enabled: !!import.meta.env.VITE_PADDLE_VENDOR_ID,
      publicKey: import.meta.env.VITE_PADDLE_VENDOR_ID || '',
    }
  ];

  // Cette méthode n'a plus besoin d'exister ici, car les plans seront fournis par Redux.
  // static getPricingPlans(): PricingPlan[] { return this.PRICING_PLANS; }

  static getAvailableProviders(): PaymentProvider[] {
    return this.PAYMENT_PROVIDERS.filter(provider => provider.enabled);
  }

  // Cette méthode prend maintenant les plans de prix en argument
  static async createCheckoutSession(
    planId: string,
    userId: string,
    pricingPlans: PricingPlan[], // <-- NOUVEL ARGUMENT
    provider: 'stripe' | 'paddle' = 'stripe'
  ): Promise<{
    success: boolean;
    checkoutUrl?: string;
    error?: string;
  }> {
    try {
      // Recherchez le plan dans le tableau fourni en argument
      const plan = pricingPlans.find(p => p.id === planId);
      if (!plan) {
        throw new Error('Plan not found for the given ID');
      }

      if (provider === 'stripe') {
        return await this.createStripeCheckout(plan, userId);
      } else if (provider === 'paddle') {
        return await this.createPaddleCheckout(plan, userId);
      }

      throw new Error('Unsupported payment provider');
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  private static async createStripeCheckout(plan: PricingPlan, userId: string): Promise<{
    success: boolean;
    checkoutUrl?: string;
    error?: string;
  }> {
    try {
      const createCheckoutSession = httpsCallable(functions, 'createStripeCheckoutSession');

      const result = await createCheckoutSession({
        priceId: plan.stripePriceId,
        userId: userId,
        successUrl: `${window.location.origin}/monetization?success=true`,
        cancelUrl: `${window.location.origin}/monetization?canceled=true`,
      });

      const data = result.data as any;

      if (data.success && data.checkoutUrl) {
        return {
          success: true,
          checkoutUrl: data.checkoutUrl
        };
      }

      throw new Error(data.error || 'Failed to create checkout session');
    } catch (error: any) {
      console.error('Stripe checkout error:', error);
      // Correction ici: Utilisez la constante IS_DEVELOPMENT
      if (IS_DEVELOPMENT) {
        return this.simulateCheckout(plan, userId);
      }

      throw error;
    }
  }

  private static async createPaddleCheckout(plan: PricingPlan, userId: string): Promise<{
    success: boolean;
    checkoutUrl?: string;
    error?: string;
  }> {
    try {
      const createCheckoutSession = httpsCallable(functions, 'createPaddleCheckoutSession');

      const result = await createCheckoutSession({
        priceId: plan.paddlePriceId,
        userId: userId,
        successUrl: `${window.location.origin}/monetization?success=true`,
        cancelUrl: `${window.location.origin}/monetization?canceled=true`,
      });

      const data = result.data as any;

      if (data.success && data.checkoutUrl) {
        return {
          success: true,
          checkoutUrl: data.checkoutUrl
        };
      }

      throw new Error(data.error || 'Failed to create checkout session');
    } catch (error: any) {
      console.error('Paddle checkout error:', error);
      // Correction ici: Utilisez la constante IS_DEVELOPMENT
      if (IS_DEVELOPMENT) {
        return this.simulateCheckout(plan, userId);
      }

      throw error;
    }
  }

  private static async simulateCheckout(plan: PricingPlan, userId: string): Promise<{
    success: boolean;
    checkoutUrl?: string;
    error?: string;
  }> {
    console.log(`Simulating checkout for plan ${plan.id} and user ${userId}`);

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          checkoutUrl: `${window.location.origin}/monetization?success=true&simulation=true`
        });
      }, 1000);
    });
  }

  static async createCustomerPortalSession(userId: string, provider: 'stripe' | 'paddle' = 'stripe'): Promise<{
    success: boolean;
    portalUrl?: string;
    error?: string;
  }> {
    try {
      if (provider === 'stripe') {
        const createPortalSession = httpsCallable(functions, 'createStripePortalSession');

        const result = await createPortalSession({
          userId: userId,
          returnUrl: `${window.location.origin}/monetization`,
        });

        const data = result.data as any;

        if (data.success && data.portalUrl) {
          return {
            success: true,
            portalUrl: data.portalUrl
          };
        }

        throw new Error(data.error || 'Failed to create portal session');
      }

      throw new Error('Customer portal not available for this provider');
    } catch (error: any) {
      console.error('Error creating customer portal session:', error);
      // Correction ici: Utilisez la constante IS_DEVELOPMENT
      if (IS_DEVELOPMENT) {
        return {
          success: true,
          portalUrl: `${window.location.origin}/monetization?portal=true&simulation=true`
        };
      }

      return {
        success: false,
        error: error.message
      };
    }
  }

  static async verifySubscription(userId: string): Promise<SubscriptionStatus> {
    try {
      const verifySubscription = httpsCallable(functions, 'verifySubscription');

      const result = await verifySubscription({ userId });
      const data = result.data as any;

      return data.subscription as SubscriptionStatus;
    } catch (error: any) {
      console.error('Error verifying subscription:', error);
      // Correction ici: Utilisez la constante IS_DEVELOPMENT
      if (IS_DEVELOPMENT) {
        return {
          active: false,
          tier: 'free',
          startDate: null,
          endsAt: null,
          cancelAtPeriodEnd: false
        };
      }

      throw error;
    }
  }

  static formatPrice(price: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(price);
  }

  // Cette méthode doit être déplacée là où vous avez accès aux `pricingPlans` du store.
  // Par exemple, dans le composant MonetizationPage ou un sélecteur Redux.
  // static calculateYearlySavings(): number { ... }
}