import React, { createContext, useContext, useEffect, useState } from 'react';
import { ThemeProvider as StyledThemeProvider } from 'styled-components';
import { lightTheme, darkTheme } from './themes';


type ThemeContextType = {
  darkMode: boolean;
  toggleTheme: () => void;
  setDarkMode: (value: boolean) => void; // <--- C'est cette ligne qui manquait ou était incorrecte
};

const ThemeContext = createContext<ThemeContextType>({
  darkMode: false,
  toggleTheme: () => {},
  setDarkMode: () => {}, // <--- Et cette initialisation
});

export const useTheme = () => useContext(ThemeContext);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [darkMode, _setDarkMode] = useState(false);

  // Fonction publique pour définir le mode sombre
  const setDarkMode = (value: boolean) => {
    _setDarkMode(value);
    localStorage.setItem('theme', value ? 'dark' : 'light'); // Mettez à jour localStorage
  };

  useEffect(() => {
    const saved = localStorage.getItem('theme');
    if (saved === 'dark') setDarkMode(true);
    else setDarkMode(false);
  }, []);

  const toggleTheme = () => {
    setDarkMode(!darkMode); // Utilise la fonction publique
  };

  return (
    <ThemeContext.Provider value={{ darkMode, toggleTheme, setDarkMode }}> {/* <--- Et l'ajout ici */}
      <StyledThemeProvider theme={darkMode ? darkTheme : lightTheme}>
        {children}
      </StyledThemeProvider>
    </ThemeContext.Provider>
  );
};