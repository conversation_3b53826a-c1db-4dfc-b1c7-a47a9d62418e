import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import styled, { keyframes, css } from 'styled-components';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store/hooks';

import { Session } from '../models'; // Assurez-vous que cette importation est correcte
// CORRECTION ICI: Importez updateUserProfile et UserAudioPreferences
import { updateUserProfile, UserAudioPreferences } from '../store/slices/userProfileSlice';
// AudioConfig n'est pas exporté par userProfileSlice, il fait partie de Session (models.ts)
// Si vous aviez une `AudioConfig` spécifique à l'utilisateur, elle est maintenant `UserAudioPreferences`.
// import { AudioConfig } from '../store/slices/userProfileSlice'; // <-- SUPPRIMER CETTE LIGNE
import { AdsService } from '../services/adsService';

import {
  FiPlay, FiPause, FiChevronLeft, FiLoader, FiVolume2, FiMusic, FiRadio, FiSettings, FiMessageCircle, FiMaximize, FiMinimize, FiRefreshCw,
  FiLock, FiUnlock, FiGift, FiZap, FiAlertCircle, FiUser
} from 'react-icons/fi';
import { ttsPlay, ttsStop, TTSConfig, TTSProvider } from '../services/tts';
import { useAuth } from '../hooks/useAuth';

// --- Styled Components (inchangés ou adaptés) ---
const PlayerPageContainer = styled.div<{ $isPlaying?: boolean }>`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  padding: 1rem;
  text-align: center;
  color: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 2000;
  background: ${({ theme, $isPlaying }) =>
    $isPlaying
      ? `linear-gradient(-45deg, ${theme.accent || '#B084CC'}, ${theme.primary || '#8A63D2'}, #23a6d5, #23d5ab)`
      : theme.background
  };
  background-size: ${({ $isPlaying }) => $isPlaying ? '400% 400%' : 'cover'};
  animation: ${({ $isPlaying }) => $isPlaying ? css`${relaxingGradient} 18s ease infinite` : 'none'};
  transition: background 0.5s ease-in-out;
`;

const relaxingGradient = keyframes`
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
`;

const BackgroundVideo = styled.video`
  position: fixed;
  right: 0;
  bottom: 0;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  object-fit: cover;
  filter: brightness(0.6) blur(3px);
`;

const ContentOverlay = styled.div`
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  height: 100%;
  width: 100%;
  max-width: 800px;
  padding: 2rem 1rem;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
`;


const TopControls = styled.div`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  right: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
`;

const BackButtonPlayer = styled.button`
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  width: auto;
  padding: 0 1rem;
  height: 44px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  gap: 0.5rem;
  &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const FullscreenButton = styled.button`
  background: rgba(255,255,255,0.15);
  color: white;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 50%;
  width: 44px;
  height: 44px;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const SessionTitle = styled.h1`
  font-size: 2rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 5px rgba(0,0,0,0.5);
  color: ${({ theme }) => theme.textLight || '#f0f0f0'};
`;

const CurrentScriptText = styled.p`
  font-size: 1.4rem;
  font-weight: 300;
  line-height: 1.9;
  margin: 1rem 0;
  padding: 1rem;
  min-height: 150px;
  max-width: 90%;
  color: ${({ theme }) => theme.textLight || '#e0e0e0'};
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
  overflow-y: auto;
  max-height: 40vh;
`;

const ControlsContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-top: 1rem;
`;

const MainControls = styled.div`
  display: flex;
  gap: 1.5rem;
  align-items: center;

  button {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.4);
    border-radius: 50%;
    width: 70px;
    height: 70px;
    font-size: 2.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    transition: background-color 0.2s, transform 0.1s;
    &:hover {
      background: rgba(255,255,255,0.3);
    }
    &:active {
      transform: scale(0.95);
    }
    &.restart-button {
      width: 50px;
      height: 50px;
      font-size: 1.5rem;
      background: rgba(255,255,255,0.15);
       &:hover {
        background: rgba(255,255,255,0.25);
      }
    }
  }
`;

const VolumePopupButton = styled.button`
  background: rgba(255,255,255,0.15);
  color: #e0e0e0;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 20px;
  padding: 0.6rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
   &:hover {
    background: rgba(255,255,255,0.25);
  }
`;

const VolumeControlPanel = styled.div<{ $show?: boolean }>`
  position: fixed;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  background: ${({ theme }) => theme.surface};
  color: ${({ theme }) => theme.text};
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 0px 20px rgba(0,0,0,0.2);
  z-index: 15;
  width: 300px;
  max-width: 90vw;
  opacity: ${({ $show }) => $show ? 1 : 0};
  visibility: ${({ $show }) => $show ? 'visible' : 'hidden'};
  transition: opacity 0.3s, visibility 0s linear ${({ $show }) => $show ? '0s' : '0.3s'};
  border: 1px solid ${({ theme }) => theme.border};

  h4 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: ${({ theme }) => theme.primary};
    text-align: left;
  }
`;

const VolumeSliderGroup = styled.div`
  margin-bottom: 1rem;
  text-align: left;
  &:last-child {
    margin-bottom: 0;
  }
  label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.3rem;
  }
  input[type="range"] {
    width: 100%;
  }
`;

const LoadingMessage = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  color: ${({ theme }) => theme.textLight || '#fff'};
  svg { font-size: 2.5rem; margin-bottom: 1rem; animation: spin 1s linear infinite; }
  @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
`;
const ErrorMessageStyled = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  min-height: 50vh;
  color: ${({ theme }) => theme.textLight || '#fff'};
  p {
    color: ${({ theme }) => theme.errorColor || '#ff6b6b'};
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  a {
    color: ${({ theme }) => theme.textLight || '#fff'};
    text-decoration: underline;
    font-weight: 500;
    &:hover {
      opacity: 0.8;
    }
  }
`;


// Nouveaux Styled Components pour la logique d'accès
const AccessGateContainer = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  padding: 30px;
  box-shadow: ${({ theme }) => theme.shadowSmall};
  margin-top: 30px;
  width: 100%;
  max-width: 500px;
  text-align: center;
  border: 1px solid ${({ theme }) => theme.border};
`;

const AccessMessage = styled.h3`
  color: ${({ theme }) => theme.errorColor};
  font-size: 1.4rem;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
`;

const AccessButtons = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
`;

const ActionButton = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  background-color: ${({ theme, $variant }) => $variant === 'primary' ? theme.primary : theme.secondary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;

  &:hover {
    background-color: ${({ theme, $variant }) => $variant === 'primary' ? theme.primaryHover : theme.accentHover};
    transform: translateY(-1px);
  }
  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
  }
`;

const AdTimer = styled.p`
  margin-top: 15px;
  font-size: 1em;
  color: ${({ theme }) => theme.textSecondary};
`;


// --- Fullscreen API Helper (inchangé) ---
const isFullScreen = () => document.fullscreenElement != null;
const requestFullScreen = (element: HTMLElement) => {
  if (element.requestFullscreen) element.requestFullscreen().catch(err => console.error("Fullscreen error:", err));
  else if ((element as any).mozRequestFullScreen) (element as any).mozRequestFullScreen();
  else if ((element as any).webkitRequestFullscreen) (element as any).webkitRequestFullscreen();
  else if ((element as any).msRequestFullscreen) (element as any).msRequestFullscreen();
};
const exitFullScreen = () => {
  if (document.exitFullscreen) document.exitFullscreen().catch(err => console.error("Exit fullscreen error:", err));
  else if ((document as any).mozCancelFullScreen) (document as any).mozCancelFullScreen();
  else if ((document as any).webkitExitFullscreen) (document as any).webkitExitFullscreen();
  else if ((document as any).msExitFullscreen) (document as any).msExitFullscreen();
};


const PlayerPage: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { lang: appLang } = useLang();
  const { t } = useTranslation();
  const playerPageRef = useRef<HTMLDivElement>(null);
  const volumeControlPanelRef = useRef<HTMLDivElement>(null);

  const dispatch = useAppDispatch();
  const { user } = useAuth(); // Récupère l'utilisateur sérialisable
  const sessions = useAppSelector(state => state.sessions.sessions);
  const sessionsLoading = useAppSelector(state => state.sessions.loading);
  // Utilise le hook usePricing pour les données de prix
  const { profile: userProfile, loading: profileLoading, saving: profileSaving } = useAppSelector(state => state.userProfile);

  const [session, setSession] = useState<Session | undefined>(undefined);
  const [pageLoading, setPageLoading] = useState(true);
  const [localError, setLocalError] = useState<string | null>(null);

  const [currentScriptIndex, setCurrentScriptIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVolumeControls, setShowVolumeControls] = useState(false);
  const [showAccessGate, setShowAccessGate] = useState(false);
  const [adLoading, setAdLoading] = useState(false);
  const [isInFullScreen, setIsInFullScreen] = useState(isFullScreen());


  // Audio Config States (maintenant initialisés via userProfile.preferences.audioConfig si disponible)
  const [enableMusic, setEnableMusic] = useState(false);
  const [musicVolume, setMusicVolume] = useState(0.5);
  const [musicFileUrl, setMusicFileUrl] = useState<string | undefined>(undefined);

  const [enableAmbient, setEnableAmbient] = useState(false);
  const [ambientVolume, setAmbientVolume] = useState(0.3);
  const [ambientFileUrl, setAmbientFileUrl] = useState<string | undefined>(undefined);

  const [enableBinaural, setEnableBinaural] = useState(false);
  const [binauralVolume, setBinauralVolume] = useState(0.2);
  const [binauralBaseFreq, setBinauralBaseFreq] = useState(100);
  const [binauralBeatFreq, setBinauralBeatFreq] = useState(10);

  const [voiceVolume, setVoiceVolume] = useState(1);
  const [ttsProvider, setTtsProvider] = useState<TTSProvider>('browser');
  const [ttsVoice, setTtsVoice] = useState<string>('auto');


  // Audio Refs
  const musicAudioRef = useRef<HTMLAudioElement | null>(null);
  const ambientAudioRef = useRef<HTMLAudioElement | null>(null);
  const ttsAbortController = useRef<AbortController | null>(null);
  const scriptPlayerTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Binaural Refs
  const audioCtxRef = useRef<AudioContext | null>(null);
  const binauralOscs = useRef<{left?: OscillatorNode, right?: OscillatorNode, gain?: GainNode}>({});

  const mountedRef = useRef(true);

  const toggleFullScreen = useCallback(() => {
    if (!playerPageRef.current) return;
    if (isInFullScreen) exitFullScreen();
    else requestFullScreen(playerPageRef.current);
  }, [isInFullScreen]);

  // Effet pour le plein écran au démarrage et gestion des changements
  useEffect(() => {
    const handleChange = () => setIsInFullScreen(isFullScreen());
    document.addEventListener('fullscreenchange', handleChange);
    document.addEventListener('webkitfullscreenchange', handleChange);
    document.addEventListener('mozfullscreenchange', handleChange);
    document.addEventListener('MSFullscreenChange', handleChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleChange);
      document.removeEventListener('webkitfullscreenchange', handleChange);
      document.removeEventListener('mozfullscreenchange', handleChange);
      document.removeEventListener('MSFullscreenChange', handleChange);
    };
  }, []);

  // Effet pour fermer le popup de volume en cliquant à l'extérieur
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showVolumeControls && volumeControlPanelRef.current && !volumeControlPanelRef.current.contains(event.target as Node)) {
        setShowVolumeControls(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showVolumeControls]);

  // Effet pour charger la session et initialiser les configurations audio
  useEffect(() => {
    setPageLoading(true);
    setLocalError(null);

    // Attendre que les sessions soient chargées
    if (sessionsLoading) {
      return;
    }

    if (!sessionId) {
      setLocalError(t('errors.missingSessionId'));
      setPageLoading(false);
      return;
    }

    const foundSession = sessions.find(s => s.id === sessionId);
    setSession(foundSession);

    if (!foundSession) {
      setLocalError(t('errors.sessionNotFound', { id: sessionId, lang: appLang }));
      setPageLoading(false);
      return;
    }

    // Initialisation des configurations audio
    // Si l'utilisateur est connecté et le profil est chargé, utiliser les préférences
    // Sinon, utiliser les paramètres par défaut de la session
    const defaultAudioConfig: UserAudioPreferences = {
      enableMusic: foundSession.audio?.enableMusic ?? false,
      music: { volume: foundSession.audio?.music?.volume ?? 0.5 },
      enableAmbient: foundSession.audio?.enableAmbient ?? false,
      ambient: { volume: foundSession.audio?.ambient?.volume ?? 0.3 },
      enableBinaural: foundSession.audio?.enableBinaural ?? false,
      binaural: {
        volume: foundSession.audio?.binaural?.volume ?? 0.2,
        baseFreq: foundSession.audio?.binaural?.baseFreq ?? 100,
        beatFreq: foundSession.audio?.binaural?.beatFreq ?? 10,
      },
      voice: {
        volume: foundSession.audio?.voice?.volume ?? 1,
        provider: 'browser', // Par défaut
        voice: 'auto', // Par défaut
      },
    };

    if (user && userProfile && userProfile.preferences) {
      // Fusionne les préférences utilisateur avec les valeurs par défaut de la session
      const userAudioConfig = userProfile.preferences.audioConfig || {};
      const mergedAudioConfig = {
        enableMusic: userAudioConfig.enableMusic ?? defaultAudioConfig.enableMusic,
        music: { volume: userAudioConfig.music?.volume ?? defaultAudioConfig.music?.volume },
        enableAmbient: userAudioConfig.enableAmbient ?? defaultAudioConfig.enableAmbient,
        ambient: { volume: userAudioConfig.ambient?.volume ?? defaultAudioConfig.ambient?.volume },
        enableBinaural: userAudioConfig.enableBinaural ?? defaultAudioConfig.enableBinaural,
        binaural: {
          volume: userAudioConfig.binaural?.volume ?? defaultAudioConfig.binaural?.volume,
          baseFreq: userAudioConfig.binaural?.baseFreq ?? defaultAudioConfig.binaural?.baseFreq,
          beatFreq: userAudioConfig.binaural?.beatFreq ?? defaultAudioConfig.binaural?.beatFreq,
        },
        voice: {
          volume: userAudioConfig.voice?.volume ?? defaultAudioConfig.voice?.volume,
          provider: userAudioConfig.voice?.provider ?? defaultAudioConfig.voice?.provider,
          voice: userAudioConfig.voice?.voice ?? defaultAudioConfig.voice?.voice,
        },
      };

      setEnableMusic(mergedAudioConfig.enableMusic || false);
      setMusicVolume(mergedAudioConfig.music?.volume || 0.5);
      setMusicFileUrl(foundSession.audio?.music?.url); // L'URL vient de la session

      setEnableAmbient(mergedAudioConfig.enableAmbient || false);
      setAmbientVolume(mergedAudioConfig.ambient?.volume || 0.3);
      setAmbientFileUrl(foundSession.audio?.ambient?.url);

      setEnableBinaural(mergedAudioConfig.enableBinaural || false);
      setBinauralVolume(mergedAudioConfig.binaural?.volume || 0.2);
      setBinauralBaseFreq(mergedAudioConfig.binaural?.baseFreq || 100);
      setBinauralBeatFreq(mergedAudioConfig.binaural?.beatFreq || 10);

      setVoiceVolume(mergedAudioConfig.voice?.volume || 1);
      setTtsProvider(mergedAudioConfig.voice?.provider || 'browser');
      setTtsVoice(mergedAudioConfig.voice?.voice || 'auto');

      setPageLoading(false);
    } else if (!user || (user && profileLoading)) { // Utilisateur non connecté OU connecté mais profil en chargement
        // S'assurer que le profil est soit chargé soit l'utilisateur non connecté.
        // Si user est connecté mais profileLoading est true (le profil n'est pas encore chargé), on attend.
        // pageLoading restera true et l'UI affichera le loader.
        // Si l'utilisateur n'est pas connecté, on utilise les valeurs par défaut de la session.
        setEnableMusic(defaultAudioConfig.enableMusic || false);
        setMusicVolume(defaultAudioConfig.music?.volume || 0.5);
        setMusicFileUrl(foundSession.audio?.music?.url);

        setEnableAmbient(defaultAudioConfig.enableAmbient || false);
        setAmbientVolume(defaultAudioConfig.ambient?.volume || 0.3);
        setAmbientFileUrl(foundSession.audio?.ambient?.url);

        setEnableBinaural(defaultAudioConfig.enableBinaural || false);
        setBinauralVolume(defaultAudioConfig.binaural?.volume || 0.2);
        setBinauralBaseFreq(defaultAudioConfig.binaural?.baseFreq || 100);
        setBinauralBeatFreq(defaultAudioConfig.binaural?.beatFreq || 10);

        setVoiceVolume(defaultAudioConfig.voice?.volume || 1);
        setTtsProvider(defaultAudioConfig.voice?.provider || 'browser');
        setTtsVoice(defaultAudioConfig.voice?.voice || 'auto');

        setPageLoading(false);
    }
  }, [
    sessionId, user, profileLoading, sessions, sessionsLoading, userProfile, appLang, t
  ]);


  // --- Logique Binaurale ---
  const stopBinauralSound = useCallback(() => {
    if (audioCtxRef.current) {
      try {
        binauralOscs.current.left?.stop();
        binauralOscs.current.right?.stop();
        if (audioCtxRef.current.state !== 'closed') {
          audioCtxRef.current.close().catch(e => console.warn("Erreur fermeture AudioContext:", e));
        }
      } catch (e) { console.warn("Erreur arrêt/fermeture audio binaural:", e); }
      audioCtxRef.current = null;
      binauralOscs.current = {};
    }
  }, []);

  const playBinauralSound = useCallback(() => {
    if (audioCtxRef.current && audioCtxRef.current.state === 'running' && binauralOscs.current.left) {
      return;
    }
    stopBinauralSound();

    const Ctx = window.AudioContext || (window as any).webkitAudioContext;
    if (!Ctx) { console.warn("Web Audio API non supportée."); return; }

    const newCtx = new Ctx();
    audioCtxRef.current = newCtx;

    const gainNode = newCtx.createGain();
    gainNode.gain.setValueAtTime(binauralVolume, newCtx.currentTime);
    gainNode.connect(newCtx.destination);

    const left = newCtx.createOscillator();
    const right = newCtx.createOscillator();
    left.type = right.type = 'sine';

    left.frequency.setValueAtTime(binauralBaseFreq, newCtx.currentTime);
    right.frequency.setValueAtTime(binauralBaseFreq + binauralBeatFreq, newCtx.currentTime);

    const merger = newCtx.createChannelMerger(2);
    left.connect(merger, 0, 0); right.connect(merger, 0, 1);
    merger.connect(gainNode);

    left.start(); right.start();
    binauralOscs.current = { left, right, gain: gainNode };
  }, [binauralVolume, binauralBaseFreq, binauralBeatFreq, stopBinauralSound]);

  const updateBinauralLiveParameters = useCallback(() => {
    if (audioCtxRef.current && audioCtxRef.current.state === 'running') {
      if (binauralOscs.current.gain) {
        binauralOscs.current.gain.gain.setValueAtTime(binauralVolume, audioCtxRef.current.currentTime);
      }
      if (binauralOscs.current.left && binauralOscs.current.right) {
        const now = audioCtxRef.current.currentTime;
        binauralOscs.current.left.frequency.cancelScheduledValues(now);
        binauralOscs.current.left.frequency.setValueAtTime(binauralBaseFreq, now);
        binauralOscs.current.right.frequency.cancelScheduledValues(now);
        binauralOscs.current.right.frequency.setValueAtTime(binauralBaseFreq + binauralBeatFreq, now);
      }
    }
  }, [binauralVolume, binauralBaseFreq, binauralBeatFreq]);


  // Effet principal pour la lecture du script (TTS)
  useEffect(() => {
    if (!isPlaying || !session?.script || currentScriptIndex >= session.script.length) {
      if (isPlaying && session?.script && currentScriptIndex >= session.script.length) {
        setIsPlaying(false);
        ttsStop(ttsAbortController.current);
      }
      return;
    }

    const currentLine = session.script[currentScriptIndex];
    if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
    if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);

    ttsAbortController.current = new AbortController();
    const ttsConfig: TTSConfig = {
      volume: voiceVolume, signal: ttsAbortController.current.signal,
      rate: currentLine.rate, pitch: currentLine.pitch,
    };

    ttsPlay(ttsProvider, currentLine.text, ttsVoice, appLang, ttsConfig)
      .then(() => {
        if (mountedRef.current && isPlaying && ttsAbortController.current && !ttsAbortController.current.signal.aborted) {
          scriptPlayerTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current && isPlaying) setCurrentScriptIndex(prev => prev + 1);
          }, currentLine.pause || 1000);
        }
      })
      .catch(err => {
        if (err.name !== 'AbortError') {
          console.error("TTS Playback Error:", err);
          if (mountedRef.current && isPlaying && ttsAbortController.current && !ttsAbortController.current.signal.aborted) {
            scriptPlayerTimeoutRef.current = setTimeout(() => {
              if (mountedRef.current && isPlaying) setCurrentScriptIndex(prev => prev + 1);
            }, currentLine.pause || 1000);
          }
        }
      });

    return () => {
      if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
      if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);
    };
  }, [isPlaying, currentScriptIndex, session, appLang, voiceVolume, ttsProvider, ttsVoice]);


  // Effet pour gérer la lecture des audios (musique, ambiance, binaural)
  useEffect(() => {
    const musicEl = musicAudioRef.current;
    const ambientEl = ambientAudioRef.current;

    const manageAudioFile = (audioElement: HTMLAudioElement | null, url: string | undefined, volume: number, enabled: boolean) => {
      if (!audioElement) return;
      if (url && enabled) {
        if (!audioElement.src || !audioElement.src.endsWith(url.split('/').pop()!)) {
            audioElement.src = url;
            audioElement.load();
        }
        audioElement.volume = volume;
        if (isPlaying) audioElement.play().catch(e => console.warn(`Audio play error (${url}):`, e));
        else audioElement.pause();
      } else {
        audioElement.pause();
        if(audioElement.src) audioElement.src = '';
      }
    };

    manageAudioFile(musicEl, musicFileUrl, musicVolume, enableMusic);
    manageAudioFile(ambientEl, ambientFileUrl, ambientVolume, enableAmbient);

    if (enableBinaural) {
      if (isPlaying) playBinauralSound();
      else stopBinauralSound();
    } else {
      stopBinauralSound();
    }
  }, [
    isPlaying, musicFileUrl, musicVolume, enableMusic,
    ambientFileUrl, ambientVolume, enableAmbient,
    enableBinaural, playBinauralSound, stopBinauralSound
  ]);

  // Effets pour mettre à jour les volumes en direct
  useEffect(() => { if (musicAudioRef.current) musicAudioRef.current.volume = musicVolume; }, [musicVolume]);
  useEffect(() => { if (ambientAudioRef.current) ambientAudioRef.current.volume = ambientVolume; }, [ambientVolume]);
  useEffect(() => { updateBinauralLiveParameters(); }, [binauralVolume, binauralBaseFreq, binauralBeatFreq, updateBinauralLiveParameters]);

  // Gestion du montage/démontage
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      musicAudioRef.current?.pause();
      if (musicAudioRef.current) musicAudioRef.current.src = '';
      ambientAudioRef.current?.pause();
      if (ambientAudioRef.current) ambientAudioRef.current.src = '';
      stopBinauralSound();
      if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
      if (scriptPlayerTimeoutRef.current) clearTimeout(scriptPlayerTimeoutRef.current);
      if (isFullScreen()) exitFullScreen();
    };
  }, [stopBinauralSound]);

  // Déclaration des fonctions de gestion des contrôles
  const handlePlayToggle = () => {
    if (!session?.script?.length && currentScriptIndex === 0) return;
    setIsPlaying(prev => !prev);
  };

  const handleRestart = () => {
    if (ttsAbortController.current && !ttsAbortController.current.signal.aborted) ttsAbortController.current.abort();
    setCurrentScriptIndex(0);
    setIsPlaying(false);
  };

  const handleWatchAd = async () => {
    if (!user) { // Si l'utilisateur n'est pas connecté, le rediriger
      navigate('/auth');
      return;
    }
    // Vérifier que le profil utilisateur est bien chargé
    if (!userProfile || !session) {
      setLocalError(t('errors.profileOrSessionMissing')); // Utilisez un message d'erreur plus approprié
      return;
    }

    const canWatchResult = AdsService.canWatchAd(userProfile);
    if (!canWatchResult.canWatch) {
      setLocalError(t('player.ad_limit_reached'));
      return;
    }

    setAdLoading(true);
    setLocalError(null);

    try {
      // Dans un environnement de production, AdsService.showAd() afficherait une vraie pub.
      // Dans le dev, cela peut simuler ou afficher un test ad.
      const adResult = await AdsService.showAd();

      if (adResult.success) {
        const now = new Date();
        const config = AdsService.getConfig();
        const adFreeUntil = new Date(now.getTime() + config.adFreeDurationMs).toISOString();

        const today = now.toISOString().substring(0, 10);
        
        // Calculer les publicités vues aujourd'hui
        let currentAdsWatchedToday = userProfile.preferences.adWatchData.adsWatchedToday || 0;
        let lastAdWatchDay = userProfile.preferences.adWatchData.lastAdWatchDay;

        if (lastAdWatchDay !== today) { // Si le dernier jour de pub est différent d'aujourd'hui, réinitialiser le compteur
          currentAdsWatchedToday = 0;
        }
        currentAdsWatchedToday++; // Incrémenter le compteur

        // --- CORRECTION CLÉ : Mettre à jour le profil utilisateur via updateUserProfile ---
        const dataToUpdate = {
            preferences: {
                ...userProfile.preferences,
                adWatchData: {
                    lastAdWatchedAt: now.toISOString(),
                    adFreeUntil: adFreeUntil,
                    adsWatchedToday: currentAdsWatchedToday,
                    lastAdWatchDay: today,
                },
            },
        };
        await dispatch(updateUserProfile({ userId: userProfile.uid, data: dataToUpdate })).unwrap();

        setShowAccessGate(false);
        setIsPlaying(true); // Lancer la lecture après la pub réussie
        console.log(`Access granted via ${adResult.provider} ad until:`, adFreeUntil);
      } else {
        setLocalError(adResult.error || t('player.ad_failed'));
      }
    } catch (error) {
      console.error("Error watching ad or updating profile:", error);
      setLocalError(t('player.ad_error_unexpected', { error: (error as Error).message }));
    } finally {
      setAdLoading(false);
    }
  };

  const adFreeTimeRemaining = userProfile?.preferences.adWatchData.adFreeUntil ?
    Math.max(0, new Date(userProfile.preferences.adWatchData.adFreeUntil).getTime() - Date.now()) : 0;

  // Logique d'accès avec le nouveau service
  useEffect(() => {
    if (!session || !userProfile || !user) {
      // Si la session, l'utilisateur ou le profil n'est pas encore chargé, ne rien faire
      setShowAccessGate(false);
      return;
    }

    // Vérifier l'accès au contenu
    const accessResult = AdsService.canAccessContent(session.category, userProfile);

    // Si l'accès est refusé et qu'une pub est requise, afficher la porte d'accès
    if (!accessResult.canAccess && accessResult.requiresAd) {
      setShowAccessGate(true);
      setIsPlaying(false); // S'assurer que le lecteur est en pause si l'accès est bloqué
    } else {
      setShowAccessGate(false);
    }
  }, [session, userProfile, user]); // Dépend de userProfile, user et session pour la ré-évaluation

  const isScriptEnded = currentScriptIndex >= (session?.script?.length || 0);
  const currentLineText = session?.script && !isScriptEnded
                          ? session.script[currentScriptIndex].text
                          : (isScriptEnded ? t('player.sessionEnded') : t('player.readyToStart'));

  const playPauseButtonTitle = isPlaying && !isScriptEnded ? t('actions.pause') : t('actions.play');
  const playPauseButtonIcon = isPlaying && !isScriptEnded ? <FiPause /> : <FiPlay />;

  // Conditions de chargement et d'erreur pour l'UI principale du PlayerPage
  if (pageLoading || sessionsLoading || (user && profileLoading)) {
    return <PlayerPageContainer><LoadingMessage><FiLoader /> {t('player.loading_data')}</LoadingMessage></PlayerPageContainer>;
  }

  if (!session) {
    return <PlayerPageContainer><ErrorMessageStyled><p>{t('errors.sessionNotFound')}</p> <Link to="/sessions">{t('actions.backToSessions')}</Link></ErrorMessageStyled></PlayerPageContainer>;
  }

  if (localError) { // Les erreurs locales sont affichées dans l'AccessGate ou ici si pas de porte
    return (
      <PlayerPageContainer>
        <ErrorMessageStyled><p>{localError}</p>
        <Link to="/sessions">{t('actions.backToSessions')}</Link>
        </ErrorMessageStyled>
      </PlayerPageContainer>
    );
  }

  return (
    <PlayerPageContainer ref={playerPageRef} $isPlaying={isPlaying && !isScriptEnded && !showAccessGate}>
      {session.imageUrl && <BackgroundVideo src={session.imageUrl} autoPlay loop muted playsInline />}

      <ContentOverlay>
        <TopControls>
          <BackButtonPlayer
            onClick={() => navigate(`/sessions/${sessionId}#journal`)}
            title={t('actions.backToSessionDetails')}
          >
            <FiChevronLeft /> {t('actions.back')}
          </BackButtonPlayer>
          <FullscreenButton onClick={toggleFullScreen} title={isInFullScreen ? t('actions.exitFullscreen') : t('actions.enterFullscreen')}>
            {isInFullScreen ? <FiMinimize /> : <FiMaximize />}
          </FullscreenButton>
        </TopControls>

        <SessionTitle>{session?.title}</SessionTitle>
        <CurrentScriptText>{currentLineText}</CurrentScriptText>

        <ControlsContainer>
          <VolumePopupButton onClick={() => setShowVolumeControls(prev => !prev)}>
            <FiSettings /> {t('player.audioSettings')}
          </VolumePopupButton>

          <VolumeControlPanel $show={showVolumeControls} ref={volumeControlPanelRef}>
            <h4>{t('player.volumeControls')}</h4>
            {enableMusic && musicFileUrl && (
              <VolumeSliderGroup>
                <label htmlFor="music-vol-player"><FiMusic /> {t('player.music')}: {Math.round(musicVolume * 100)}%</label>
                <input id="music-vol-player" type="range" min="0" max="1" step="0.01" value={musicVolume} onChange={(e) => setMusicVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}
            {enableAmbient && ambientFileUrl && (
              <VolumeSliderGroup>
                <label htmlFor="ambient-vol-player"><FiRadio /> {t('player.ambient')}: {Math.round(ambientVolume * 100)}%</label>
                <input id="ambient-vol-player" type="range" min="0" max="1" step="0.01" value={ambientVolume} onChange={(e) => setAmbientVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}
            {enableBinaural && (
              <VolumeSliderGroup>
                <label htmlFor="binaural-vol-player"><FiVolume2 /> {t('player.binaural')}: {Math.round(binauralVolume * 100)}%</label>
                <input id="binaural-vol-player" type="range" min="0" max="1" step="0.01" value={binauralVolume} onChange={(e) => setBinauralVolume(Number(e.target.value))} />
              </VolumeSliderGroup>
            )}
            <VolumeSliderGroup>
              <label htmlFor="voice-vol-player"><FiMessageCircle /> {t('player.voice')}: {Math.round(voiceVolume * 100)}%</label>
              <input id="voice-vol-player" type="range" min="0" max="1" step="0.01" value={voiceVolume} onChange={(e) => setVoiceVolume(Number(e.target.value))} />
            </VolumeSliderGroup>
          </VolumeControlPanel>

          <MainControls>
            <button className="restart-button" onClick={handleRestart} title={t('actions.restart')}>
              <FiRefreshCw />
            </button>
            <button onClick={handlePlayToggle} title={playPauseButtonTitle} disabled={isScriptEnded && !isPlaying}>
              {playPauseButtonIcon}
            </button>
          </MainControls>
        </ControlsContainer>
      </ContentOverlay>

      {/* Les éléments audio sont toujours présents mais contrôlés par JS */}
      <audio ref={musicAudioRef} loop />
      <audio ref={ambientAudioRef} loop />

      {/* Logique d'accès au lecteur */}
      {showAccessGate && (
        <AccessGateContainer>
          <AccessMessage>
            <FiLock /> {t('player.access_denied_title')}
          </AccessMessage>
          <p>{t('player.access_denied_message')}</p>
          {localError && <ErrorMessageStyled><p>{localError}</p></ErrorMessageStyled>}
          <AccessButtons>
            {user ? ( // Si l'utilisateur est connecté, proposer pub ou premium
                <>
                    <ActionButton onClick={handleWatchAd} disabled={adLoading || profileSaving}>
                      {adLoading ? <FiLoader /> : <FiGift />} {adLoading ? t('player.watching_ad_loading') : t('player.watch_ad_button')}
                    </ActionButton>
                    <ActionButton $variant="secondary" onClick={() => navigate('/monetization')} disabled={profileSaving}>
                      <FiZap /> {t('player.upgrade_premium_button')}
                    </ActionButton>
                </>
            ) : ( // Si l'utilisateur n'est PAS connecté, proposer de se connecter
                <ActionButton onClick={() => navigate('/auth')} $variant="primary">
                    <FiUser /> {t('auth.login.button')} / {t('auth.signup.button')}
                </ActionButton>
            )}
          </AccessButtons>
          {adFreeTimeRemaining > 0 && (
            <AdTimer>
              {t('player.ad_free_period', { time: AdsService.formatTimeRemaining(adFreeTimeRemaining) })}
            </AdTimer>
          )}
        </AccessGateContainer>
      )}
    </PlayerPageContainer>
  );
};

export default PlayerPage;