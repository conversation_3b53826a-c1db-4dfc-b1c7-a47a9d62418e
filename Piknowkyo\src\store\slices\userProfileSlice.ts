// src/store/slices/userProfileSlice.ts (VERSION ADAPTÉE À VOTRE TTSProvider)
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../../firebase';

// Importez le type TTSProvider depuis votre fichier tts.ts
import { TTSProvider } from '../../services/tts'; // <-- NOUVEL IMPORT ICI

// --- INTERFACE CORRIGÉE : Pour les préférences audio de l'utilisateur ---
export interface UserAudioPreferences {
  enableMusic?: boolean;
  music?: {
    volume?: number;
  };
  enableAmbient?: boolean;
  ambient?: {
    volume?: number;
  };
  enableBinaural?: boolean;
  binaural?: {
    volume?: number;
    baseFreq?: number;
    beatFreq?: number;
  };
  voice?: {
    volume?: number;
    provider?: TTSProvider; // <-- UTILISE LE TYPE TTSProvider DE VOTRE TTS.TS
    voice?: string; // Nom/ID de la voix spécifique
  };
}

export interface UserProfileData {
  uid: string;
  email: string;
  displayName: string | null;
  createdAt?: string; // Was Date
  lastLoginAt?: string; // Was Date
  preferences: {
    theme: 'light' | 'dark';
    language: 'en' | 'fr';
    subscriptions: {
      active: boolean;
      tier: 'free' | 'premium';
      startDate: string | null;
      endsAt: string | null;
    };
    adWatchData: {
      lastAdWatchDay: string | null;
      adsWatchedToday: number;
      adFreeUntil: string | null;
    };
    stats: {
      totalSessionsCompleted: 0;
      totalMinutesMeditated: 0;
      longestStreak: 0;
      currentStreak: 0;
      lastSessionPlayedAt: string | null; // Was null, potentially from Date
    };
    audioConfig?: UserAudioPreferences;
  };
  // Ajoutez d'autres champs si nécessaire
}

interface UserProfileState {
  profile: UserProfileData | null;
  loading: boolean;
  saving: boolean;
  error: string | null;
}

const initialState: UserProfileState = {
  profile: null,
  loading: false,
  saving: false,
  error: null,
};

// Define a type for the update payload
export type UserProfileUpdatePayload = {
  userId: string;
  data: Partial<Omit<UserProfileData, 'uid' | 'email'>>; // Allow updating parts of UserProfileData, omitting uid/email which are identifiers
};

const convertFirestoreTimestampsToISOStrings = (data: any): any => {
  const newPrefs = data.preferences ? { ...data.preferences } : {};
  let changed = false;

  if (data.createdAt && typeof data.createdAt.toDate === 'function') {
    data.createdAt = data.createdAt.toDate().toISOString();
    changed = true;
  }
  if (data.lastLoginAt && typeof data.lastLoginAt.toDate === 'function') {
    data.lastLoginAt = data.lastLoginAt.toDate().toISOString();
    changed = true;
  }

  if (newPrefs.subscriptions) {
    if (newPrefs.subscriptions.startDate && typeof newPrefs.subscriptions.startDate.toDate === 'function') {
      newPrefs.subscriptions.startDate = newPrefs.subscriptions.startDate.toDate().toISOString();
      changed = true;
    }
    if (newPrefs.subscriptions.endsAt && typeof newPrefs.subscriptions.endsAt.toDate === 'function') {
      newPrefs.subscriptions.endsAt = newPrefs.subscriptions.endsAt.toDate().toISOString();
      changed = true;
    }
  }
  if (newPrefs.adWatchData) {
    if (newPrefs.adWatchData.adFreeUntil && typeof newPrefs.adWatchData.adFreeUntil.toDate === 'function') {
       newPrefs.adWatchData.adFreeUntil = newPrefs.adWatchData.adFreeUntil.toDate().toISOString();
       changed = true;
    }
    // Assuming lastAdWatchedAt is already a string or null
  }
  if (newPrefs.stats) {
    if (newPrefs.stats.lastSessionPlayedAt && typeof newPrefs.stats.lastSessionPlayedAt.toDate === 'function') {
      newPrefs.stats.lastSessionPlayedAt = newPrefs.stats.lastSessionPlayedAt.toDate().toISOString();
      changed = true;
    }
  }

  if (changed) {
    data.preferences = newPrefs;
  }
  return data;
};


export const fetchUserProfile = createAsyncThunk(
  'userProfile/fetchUserProfile',
  async (uid: string, { rejectWithValue }) => {
    if (!uid) {
      console.error("fetchUserProfile thunk called without uid.");
      return rejectWithValue('User ID is missing for fetching profile.');
    }
    try {
      const userDocRef = doc(db, 'users', uid);
      const docSnap = await getDoc(userDocRef);

      if (docSnap.exists()) {
        const profileData = convertFirestoreTimestampsToISOStrings(docSnap.data() as UserProfileData); // Adjusted
        return { uid: docSnap.id, ...profileData };
      } else {
        return rejectWithValue('Profil utilisateur non trouvé.');
      }
    } catch (error: any) {
      console.error("Error fetching user profile:", error);
      return rejectWithValue(error.message || 'Échec du chargement du profil utilisateur.');
    }
  }
);

export const createUserProfile = createAsyncThunk(
  'userProfile/createUserProfile',
  async (initialData: { uid: string; email: string; displayName: string | null; }, { rejectWithValue }) => {
    if (!initialData || !initialData.uid) {
      console.error("createUserProfile thunk called without uid.");
      return rejectWithValue('User ID is missing for creating profile.');
    }
    try {
      const newUserProfile: UserProfileData = {
        uid: initialData.uid,
        email: initialData.email,
        displayName: initialData.displayName,
        createdAt: new Date().toISOString(), // Changed to ISOString
        lastLoginAt: new Date().toISOString(), // Changed to ISOString
        preferences: {
          theme: 'light', // Thème par défaut
          language: 'fr', // Langue par défaut
          subscriptions: {
            active: false,
            tier: 'free',
            startDate: null,
            endsAt: null,
          },
          adWatchData: {
            lastAdWatchDay: null,
            adsWatchedToday: 0,
            adFreeUntil: null,
          },
          stats: {
            totalSessionsCompleted: 0,
            totalMinutesMeditated: 0,
            longestStreak: 0,
            currentStreak: 0,
            lastSessionPlayedAt: null,
          },
          audioConfig: { // Initialisation des préférences audio par défaut
            enableMusic: false,
            music: { volume: 0.5 },
            enableAmbient: false,
            ambient: { volume: 0.3 },
            enableBinaural: false,
            binaural: { volume: 0.2, baseFreq: 100, beatFreq: 10 },
            voice: { volume: 1, provider: 'browser', voice: 'auto' }, // <-- 'browser' est votre provider par défaut compatible
          },
        },
      };

      const userDocRef = doc(db, 'users', initialData.uid);
      await setDoc(userDocRef, newUserProfile);

      return newUserProfile;
    } catch (error: any) {
      console.error("Error creating user profile:", error);
      return rejectWithValue(error.message || 'Échec de la création du profil utilisateur.');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'userProfile/updateUserProfile',
  async (payload: UserProfileUpdatePayload, { rejectWithValue }) => {
    const { userId, data } = payload;
    if (!userId) {
      console.error("updateUserProfile thunk called without userId.");
      return rejectWithValue('User ID is missing for profile update.');
    }
    try {
      const userDocRef = doc(db, 'users', userId);
      // Ensure data being sent to Firestore is clean (e.g., no undefined values if merge is not deep)
      // For setDoc with merge: true, undefined fields are typically ignored by Firestore or you might need to handle them.
      await setDoc(userDocRef, data, { merge: true });

      // For the return value, we should fetch the updated profile or construct it carefully.
      // To keep it simple and consistent with typical patterns, let's return the data that was sent for merging.
      // The state will be updated optimistically or based on a re-fetch.
      // However, the current thunk returns the input `profileData`. Let's adapt.
      // The fulfilled action expects UserProfileData. We need to simulate this.
      // This part is tricky: the thunk is expected to return the full UserProfileData.
      // A better pattern might be to return what was successfully saved,
      // and let the reducer merge it into existing state or trigger a re-fetch.
      // For now, to minimize disruption, let's assume `data` contains the fields that were updated.
      // The reducer will merge this into the existing profile.

      // The `action.payload` in the reducer needs to be UserProfileData.
      // This means we should ideally fetch the document again or be very careful.
      // Let's try returning the merged data, assuming `data` can be part of UserProfileData.
      // This is a common simplification, but can lead to stale data if not handled carefully.
      const docSnap = await getDoc(userDocRef);
      if (docSnap.exists()) {
        return convertFirestoreTimestampsToISOStrings(docSnap.data() as UserProfileData); // Adjusted
      } else {
        return rejectWithValue('Updated profile could not be refetched.');
      }
    } catch (error: any) {
      console.error("Error updating user profile in thunk:", error);
      return rejectWithValue(error.message || 'Échec de la mise à jour du profil utilisateur.');
    }
  }
);

const userProfileSlice = createSlice({
  name: 'userProfile',
  initialState,
  reducers: {
    clearUserProfile: (state) => {
      state.profile = null;
      state.error = null;
      state.loading = false;
      state.saving = false;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchUserProfile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.loading = false;
        state.profile = action.payload;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // createUserProfile
      .addCase(createUserProfile.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(createUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.saving = false;
        state.profile = action.payload;
        state.error = null;
      })
      .addCase(createUserProfile.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      })
      // updateUserProfile
      .addCase(updateUserProfile.pending, (state) => {
        state.saving = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action: PayloadAction<UserProfileData>) => {
        state.saving = false;
        state.profile = action.payload;
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.saving = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearUserProfile } = userProfileSlice.actions;
export default userProfileSlice.reducer;