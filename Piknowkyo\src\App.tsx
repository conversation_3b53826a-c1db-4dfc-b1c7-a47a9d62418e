// src/App.tsx (CORRIGÉ POUR LA LOGIQUE DE PROFIL UTILISATEUR)
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLang, Language } from './LangProvider';
import { usePushNotifications } from './services/usePushNotifications';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { FiMenu } from 'react-icons/fi';
import { useAppDispatch, useAppSelector } from './store/hooks';

// Importez vos thunks
import { fetchSessions } from './store/slices/sessionsSlice';
import { fetchJournalEntries } from './store/slices/journalSlice';
import { fetchAudioAssets } from './store/slices/audioAssetsSlice';
import { fetchHistory } from './store/slices/historySlice';
// Importez le nouveau thunk createUserProfile
import { fetchUserProfile, createUserProfile, clearUserProfile } from './store/slices/userProfileSlice';
import { fetchPricingConfig } from './store/slices/pricingSlice';

import HomePage from './pages/HomePage';
import AboutPage from './pages/AboutPage';
import SessionsPage from './pages/SessionsPage';
import SessionDetailPage from './pages/SessionDetailPage';
import PlayerPage from './pages/PlayerPage';
import JournalPage from './pages/JournalPage';
import StatsPage from './pages/StatsPage';
import LeaderboardPage from './pages/LeaderboardPage';
import BlogPage from './pages/BlogPage';
import BlogPostCommentsPage from './pages/BlogPostCommentsPage';
import GamesPage from './pages/GamesPage';
import MonetizationPage from './pages/MonetizationPage';
import CategoriesPage from './pages/CategoriesPage';
import HistoryPage from './pages/HistoryPage';
import SettingsPage from './pages/SettingsPage';
import AudioAssetsConfigPage from './pages/AudioAssetsConfigPage';
import NotFoundPage from './pages/NotFoundPage';
import ProfilePage from './pages/ProfilePage';
import ReduxExample from './components/ReduxExample';
import BottomBar from './components/BottomBar';
import NetworkStatusNotifier from './components/NetworkStatusNotifier';
import SplashScreen from './components/SplashScreen';
import MainMenu from './components/MainMenu';
import SyncStatusIndicator from './components/SyncStatusIndicator';

import { useTheme } from './ThemeProvider';
import { useAuth } from './hooks/useAuth';
import AuthPage from './components/AuthPage';
import Logout from './components/Logout';
import { AuthService } from './services/authService';

const App: React.FC = () => {
  const [showSplash, setShowSplash] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const { lang, setLang } = useLang();
  const { i18n, t } = useTranslation();
  const { darkMode, toggleTheme, setDarkMode } = useTheme();
  const dispatch = useAppDispatch();

  const { user, loading: authLoading } = useAuth(); // 'loading' est maintenant utilisé correctement
  const { profile: userProfile, loading: profileLoading } = useAppSelector(state => state.userProfile);

  usePushNotifications();

  useEffect(() => {
    AuthService.initialize();
    return () => AuthService.cleanup();
  }, []);

  useEffect(() => {
    if (i18n.language !== lang) {
      i18n.changeLanguage(lang);
    }
  }, [lang, i18n]);

  useEffect(() => {
    const timer = setTimeout(() => setShowSplash(false), 1200);
    return () => clearTimeout(timer);
  }, []);

  // --- NOUVEAU: Chargement de TOUTES les données (globales et spécifiques à l'utilisateur) LORS DE LA CONNEXION ---
  useEffect(() => {
    if (!authLoading && user) { // L'utilisateur est connecté et l'état d'authentification est stable
      // 1. Charger le profil utilisateur (ou le créer s'il n'existe pas)
      if (!userProfile && !profileLoading) {
        if (user.uid) { // Explicit UID check
          dispatch(fetchUserProfile(user.uid))
            .unwrap()
            .then(profileData => {
              if (profileData.preferences) {
              if (profileData.preferences.theme !== undefined) {
                setDarkMode(profileData.preferences.theme === 'dark');
              } else {
                const savedTheme = localStorage.getItem('theme');
                setDarkMode(savedTheme === 'dark');
              }

              if (profileData.preferences.language !== undefined) {
                setLang(profileData.preferences.language as Language);
              } else {
                const savedLang = localStorage.getItem('language');
                setLang(savedLang as Language || 'fr');
              }
            }
            // Une fois le profil chargé, on peut charger les données spécifiques à l'utilisateur
            dispatch(fetchJournalEntries(user.uid)); // uid already checked
            dispatch(fetchHistory(user.uid)); // uid already checked

            // Et les données globales qui nécessitent l'authentification
            dispatch(fetchSessions());
            dispatch(fetchAudioAssets());
            dispatch(fetchPricingConfig());
          })
          .catch(error => {
            console.error("Échec du chargement du profil utilisateur:", error);
            // Si le profil n'est pas trouvé (nouvel utilisateur), le créer
            if (error === 'Profil utilisateur non trouvé.' && user.uid && user.email) { // Explicit UID check
              console.log("Tentative de création d'un nouveau profil utilisateur...");
              dispatch(createUserProfile({ uid: user.uid, email: user.email, displayName: user.displayName }))
                .unwrap()
                .then(() => {
                  console.log("Profil utilisateur créé avec succès, re-fetch des données.");
                  // Après la création, vous pouvez re-fetcher le profil ou simplement laisser les données par défaut être utilisées.
                  // Pour s'assurer que toutes les autres données sont chargées, on les redéclenche.
                  dispatch(fetchJournalEntries(user.uid)); // uid already checked
                  dispatch(fetchHistory(user.uid)); // uid already checked
                  dispatch(fetchSessions());
                  dispatch(fetchAudioAssets());
                  dispatch(fetchPricingConfig());
                })
                .catch(createError => {
                  console.error("Échec de la création du profil utilisateur:", createError);
                  // Gérer l'erreur de création, ex: déconnecter l'utilisateur ou afficher un message
                });
            }
          });
        } else { // else for if (user.uid)
          console.error("User object is present but uid is missing in App.tsx effect when fetching profile.");
          // Potentially dispatch an error action or handle this state
        }
      } else if (userProfile) { // Si le profil est déjà chargé (ex: via persistance ou re-render)
          // Assurez-vous que les données qui dépendent du user ET du userProfile sont chargées.
        if (user.uid) { // Explicit UID check
          dispatch(fetchJournalEntries(user.uid));
          dispatch(fetchHistory(user.uid));
          dispatch(fetchSessions());
          dispatch(fetchAudioAssets());
        } else {
          console.error("User object is present but uid is missing in App.tsx effect when userProfile already exists.");
          // Potentially dispatch an error action or handle this state
        }
          dispatch(fetchPricingConfig());
      }
    } else if (!authLoading && !user) {
        // Si l'utilisateur se déconnecte après le chargement initial de l'authentification
        // Nettoyer le state Redux des données utilisateurs si elles étaient présentes.
        dispatch(clearUserProfile()); // Nettoie le profil
        // Vous pouvez ajouter d'autres actions de nettoyage pour sessions, journal, etc.
        // dispatch(clearSessions());
        // dispatch(clearJournal());
        // ...
    }
  }, [dispatch, user, authLoading, userProfile, profileLoading, setDarkMode, setLang]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const closeMenu = () => {
    setMenuOpen(false);
  };

  // Condition de rendu :
  // On affiche le SplashScreen si:
  // 1. showSplash est vrai (au démarrage de l'app)
  // 2. authLoading est vrai (Firebase vérifie l'état d'authentification)
  // 3. l'utilisateur est connecté MAIS son profil n'a pas encore été chargé (userProfile est null et profileLoading est vrai)
  //    (Et la création de profil est en cours si l'utilisateur est nouveau)
  // La splash screen devrait se masquer une fois le profil chargé/créé.
  if (showSplash || authLoading || (user && !userProfile && profileLoading)) {
    return <SplashScreen />;
  }

  // Si l'utilisateur n'est PAS connecté après le chargement initial de l'authentification
  if (!user) {
    return <AuthPage />;
  }
  // Si l'utilisateur est connecté et le profil a été chargé (ou créé après un échec initial)
  else {
    return (
      <Router>
        <div className={`app-container ${darkMode ? 'dark-theme' : 'light-theme'}`}>
          <header className="app-header">
            <div className="logo">
              <img src="/logo192.png" alt={t('app.logo_alt')} style={{ height: 40, marginRight: 12 }} />
              <span>{t('app.name')}</span>
            </div>
            <div className="header-actions">
              <SyncStatusIndicator />
              <Logout />
              <button
                className="theme-toggle"
                onClick={toggleTheme}
                aria-label={darkMode ? t('app.theme_light') : t('app.theme_dark')}
              >
                {darkMode ? '☀️' : '🌙'}
              </button>
              <button
                className="menu-toggle"
                onClick={toggleMenu}
                aria-label={t('main_menu.open_menu_aria_label')}
              >
                <FiMenu size={24} />
              </button>
            </div>
          </header>

          <MainMenu isOpen={menuOpen} onClose={closeMenu} />

          <main className="app-main">
            <Routes>
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/sessions" element={<SessionsPage />} />
              <Route path="/sessions/:sessionId" element={<SessionDetailPage />} />
              <Route path="/player/:sessionId" element={<PlayerPage />} />
              <Route path="/journal" element={<JournalPage />} />
              <Route path="/stats" element={<StatsPage />} />
              <Route path="/leaderboard" element={<LeaderboardPage />} />
              <Route path="/blog" element={<BlogPage />} />
              <Route path="/blog/:postId/comments" element={<BlogPostCommentsPage />} />
              <Route path="/games" element={<GamesPage />} />
              <Route path="/monetization" element={<MonetizationPage />} />
              <Route path="/categories" element={<CategoriesPage />} />
              <Route path="/history" element={<HistoryPage />} />
              <Route path="/settings" element={<SettingsPage />} />
              <Route path="/settings/audio-assets" element={<AudioAssetsConfigPage />} />
              <Route path="/redux-example" element={<ReduxExample />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </main>

          <NetworkStatusNotifier />
          <BottomBar />
        </div>
      </Router>
    );
  }
};

export default App;