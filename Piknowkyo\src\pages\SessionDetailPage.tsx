import React, { useEffect, useState, useContext, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import JournalEntryForm from '../components/JournalEntryForm';
import AudioConfigPanel from '../components/AudioConfigPanel';

import {
  FiClock, FiHeart, FiTag, FiMessageSquare, FiPlayCircle, FiChevronLeft, FiStar, FiLoader, FiInfo, FiSettings, FiEdit3,
  FiBellOff, FiBell, FiLock, FiUnlock, FiDollarSign, FiGift, FiZap, FiUser // Ajout de FiUser pour bouton Connexion
} from 'react-icons/fi';
import { useLang } from '../LangProvider';
import { useTranslation } from 'react-i18next';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { Session } from '../models';
import { useAuth } from '../hooks/useAuth';
import { UserProfileData, AudioConfig, updateUserProfile } from '../store/slices/userProfileSlice';

// --- Helper pour les permissions de notification (Web) ---
const checkNotificationPermission = (): NotificationPermission => {
  if (!("Notification" in window)) {
    return "denied";
  }
  return Notification.permission;
};

const requestNotificationPermission = async (t: any): Promise<NotificationPermission> => {
  if (!("Notification" in window)) {
    alert(t('notifications.notSupported', 'Ce navigateur ne supporte pas les notifications.'));
    return "denied";
  }
  if (Notification.permission === "granted") {
    return "granted";
  }
  if (Notification.permission !== "denied") {
    const permission = await Notification.requestPermission();
    return permission;
  }
  return "denied";
};


// --- Styled Components (les vôtres) ---
const PageContainer = styled.div`
  padding-bottom: calc(4rem + 60px + 1.5rem);
`;

const HeaderImage = styled.div<{ imageUrl?: string }>`
  height: 35vh;
  min-height: 280px;
  max-height: 450px;
  background-image: ${({ imageUrl, theme }) =>
    imageUrl
      ? `url(${imageUrl})`
      : `linear-gradient(135deg, ${theme.gradientStart || theme.primary}, ${theme.gradientEnd || theme.accent})`
  };
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: flex-end;
  padding: 1.5rem;
  position: relative;
  color: ${({ theme }) => theme.textLight || '#fff'};

  &::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0.15) 60%, rgba(0,0,0,0) 100%);
    z-index: 1;
  }
`;

const HeaderContent = styled.div`
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;

  h1 {
    font-size: 2.4rem;
    margin: 0 0 0.5rem 0;
    font-weight: 700;
    text-shadow: 0 2px 5px rgba(0,0,0,0.6);
  }
`;

const BackButton = styled.button`
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  background: rgba(0,0,0,0.4);
  color: white;
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 30;
  transition: background-color 0.2s;
  font-size: 1.3rem;

  &:hover {
    background: rgba(0,0,0,0.6);
  }
`;

const SessionInfoBar = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem 1.2rem;
  align-items: center;
  font-size: 0.95rem;
  opacity: 0.95;

  svg {
    margin-right: 0.4rem;
    vertical-align: middle;
  }

  span {
    display: flex;
    align-items: center;
  }
`;

const ContentWrapper = styled.div`
  padding: 1.5rem;
  max-width: 800px;
  margin: -4rem auto 0 auto;
  background-color: ${({ theme }) => theme.surface};
  border-radius: 20px;
  box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
`;

const Section = styled.section`
  margin-bottom: 2.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  h2 {
    font-size: 1.6rem;
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1.2rem;
    padding-bottom: 0.6rem;
    border-bottom: 1px solid ${({ theme }) => theme.border};
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  p, li {
    color: ${({ theme }) => theme.textSecondary};
    line-height: 1.75;
    font-size: 1rem;
  }

  ul {
    list-style: disc;
    padding-left: 1.8rem;
  }
`;

const TagsContainer = styled.div`
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  span {
    background-color: ${({ theme }) => `${theme.accent}2A`};
    color: ${({ theme }) => theme.accent};
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
  }
`;

const CommentList = styled.ul`
  list-style: none;
  padding: 0;
  li {
    background-color: ${({ theme }) => theme.surfaceAlt};
    padding: 1rem 1.2rem;
    border-radius: 10px;
    margin-bottom: 0.8rem;
    font-size: 0.95rem;
    border-left: 3px solid ${({ theme }) => theme.primary};
  }
`;

const PlayButtonFloating = styled.button`
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom, 0px) + 60px + 1.5rem);
  right: 1.5rem;
  background-color: ${({ theme }) => theme.primary};
  color: ${({ theme }) => theme.textLight || '#fff'};
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 1.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0,0,0,0.25);
  cursor: pointer;
  z-index: 100;
  transition: transform 0.2s, background-color 0.2s;

  &:hover {
    transform: scale(1.08);
    background-color: ${({ theme }) => theme.accent || theme.primary};
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.primary};
  min-height: 50vh;

  svg {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const ErrorMessageStyled = styled.p`
  color: red;
  text-align: center;
  padding: 2rem;
`;

const AccessStatus = styled.div<{ $type: 'free' | 'premium' | 'ad-supported' | 'locked' }>`
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-weight: bold;
  font-size: 0.9em;
  margin-bottom: 1.5rem;

  background-color: ${({ theme, $type }) => {
    if ($type === 'free') return theme.primary + '20';
    if ($type === 'premium') return theme.accent + '20';
    if ($type === 'ad-supported') return theme.secondary + '20';
    return theme.errorColor + '20';
  }};

  color: ${({ theme, $type }) => {
    if ($type === 'free') return theme.primary;
    if ($type === 'premium') return theme.accent;
    if ($type === 'ad-supported') return theme.secondary;
    return theme.errorColor;
  }};
`;

const GoToMonetizationButton = styled.button`
  background-color: ${({ theme }) => theme.secondary};
  color: ${({ theme }) => theme.textLight};
  border: none;
  border-radius: 8px;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.1s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: fit-content;
  margin: 0 auto 1.5rem auto;

  &:hover {
    background-color: ${({ theme }) => theme.accentHover};
    transform: translateY(-2px);
  }
  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground};
    color: ${({ theme }) => theme.disabledText};
    cursor: not-allowed;
  }
`;

const AudioToggleGroup = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: ${({ theme }) => theme.surfaceAlt};
  padding: 0.8rem 1.2rem;
  border-radius: 10px;
  border: 1px solid ${({ theme }) => theme.border};
  margin-bottom: 1rem;

  label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
    color: ${({ theme }) => theme.text};
    cursor: pointer;
    flex-grow: 1;
  }

  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    margin-left: 10px;
  }

  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }

  .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: ${({ theme }) => theme.textMuted || '#ccc'};
    transition: .4s;
    border-radius: 24px;
  }

  .slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }

  input:checked + .slider {
    background-color: ${({ theme }) => theme.primary};
  }

  input:focus + .slider {
    box-shadow: 0 0 1px ${({ theme }) => theme.primary};
  }

  input:checked + .slider:before {
    transform: translateX(20px);
  }
`;

const InfoTextSmall = styled.p`
  font-size: 0.85rem;
  color: ${({ theme }) => theme.textMuted};
  margin-top: -0.5rem;
  margin-bottom: 1rem;
`;


const SessionDetailPage: React.FC = () => {
  const { t } = useTranslation();
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const { lang: appLang } = useLang();
  const dispatch = useAppDispatch();

  const theme = useContext(ThemeContext) as DefaultTheme;

  const { user } = useAuth();
  const sessions = useAppSelector(state => state.sessions.sessions);
  const sessionsLoading = useAppSelector(state => state.sessions.loading);
  const sessionsError = useAppSelector(state => state.sessions.error);
  const { profile: userProfile, loading: profileLoading, error: profileError } = useAppSelector(state => state.userProfile);

  const [session, setSession] = useState<Session | null>(null);
  const [pageLoading, setPageLoading] = useState(true);
  const [localError, setLocalError] = useState<string | null>(null);

  const [journalNotes, setJournalNotes] = useState<string[]>([]);

  const [accessType, setAccessType] = useState<'free' | 'premium' | 'ad-supported' | 'locked'>('locked');

  // Audio Config States (initialisés à partir du userProfile.preferences.audioConfig)
  const [enableMusic, setEnableMusic] = useState(false);
  const [musicVolume, setMusicVolume] = useState(0.5);
  const [musicFileUrl, setMusicFileUrl] = useState<string>('');

  const [enableAmbient, setEnableAmbient] = useState(false);
  const [ambientVolume, setAmbientVolume] = useState(0.3);
  const [ambientFileUrl, setAmbientFileUrl] = useState<string>('');

  const [enableBinaural, setEnableBinaural] = useState(false);
  const [binauralVolume, setBinauralVolume] = useState(0.2);
  const [baseFreq, setBaseFreq] = useState(100);
  const [beatFreq, setBeatFreq] = useState(10);

  const [voiceVolume, setVoiceVolume] = useState(1);
  // const [voiceGender, setVoiceGender] = useState<'masculine' | 'feminine' | 'neutral'>('neutral');

  // State pour "Ne Pas Déranger"
  const [enableDND, setEnableDND] = useState(false);
  const [notificationPermissionStatus, setNotificationPermissionStatus] = useState<NotificationPermission>("default");

  useEffect(() => {
    setNotificationPermissionStatus(checkNotificationPermission());
  }, []);

  // Effet pour charger la session et initialiser les états audio depuis userProfile
  useEffect(() => {
    setPageLoading(true);
    setLocalError(null);

    // Attendre que les sessions soient chargées
    if (sessionsLoading) {
      return;
    }

    if (!sessionId) {
      setLocalError(t('errors.missingSessionId'));
      setPageLoading(false);
      return;
    }

    const foundSession = sessions.find(s => s.id === sessionId);
    setSession(foundSession || null);

    if (!foundSession) {
      setLocalError(t('errors.sessionNotFound', { id: sessionId, lang: appLang }));
      setPageLoading(false);
      return;
    }

    // Initialisation des configurations audio et DND à partir du userProfile
    // Si l'utilisateur est connecté et le profil est chargé, utiliser les préférences
    if (user && userProfile && userProfile.preferences) {
        const audioConfig = userProfile.preferences.audioConfig;

        setEnableMusic(audioConfig.enableMusic ?? foundSession.audio?.enableMusic ?? false);
        setMusicVolume(audioConfig.music?.volume ?? foundSession.audio?.music?.volume ?? 0.5);
        setMusicFileUrl(audioConfig.music?.url ?? foundSession.audio?.music?.url ?? '');

        setEnableAmbient(audioConfig.enableAmbient ?? foundSession.audio?.enableAmbient ?? false);
        setAmbientVolume(audioConfig.ambient?.volume ?? foundSession.audio?.ambient?.volume ?? 0.3);
        setAmbientFileUrl(audioConfig.ambient?.url ?? foundSession.audio?.ambient?.url ?? '');

        setEnableBinaural(audioConfig.enableBinaural ?? foundSession.audio?.enableBinaural ?? false);
        setBinauralVolume(audioConfig.binaural?.volume ?? foundSession.audio?.binaural?.volume ?? 0.2);
        setBaseFreq(audioConfig.binaural?.baseFreq ?? foundSession.audio?.binaural?.baseFreq ?? 100);
        setBeatFreq(audioConfig.binaural?.beatFreq ?? foundSession.audio?.binaural?.beatFreq ?? 10);

        setVoiceVolume(audioConfig.voice?.volume ?? foundSession.audio?.voice?.volume ?? 1);
        // setVoiceGender(audioConfig.voice?.gender ?? foundSession.audio?.voice?.gender ?? 'neutral');
    } else if (!user) { // Utilisateur non connecté, mais session trouvée
        // Utilisez les paramètres audio de la session par défaut pour les non-connectés
        setEnableMusic(foundSession.audio?.enableMusic ?? false);
        setMusicVolume(foundSession.audio?.music?.volume ?? 0.5);
        setMusicFileUrl(foundSession.audio?.music?.url ?? '');

        setEnableAmbient(foundSession.audio?.enableAmbient ?? false);
        setAmbientVolume(foundSession.audio?.ambient?.volume ?? 0.3);
        setAmbientFileUrl(foundSession.audio?.ambient?.url ?? '');

        setEnableBinaural(foundSession.audio?.enableBinaural ?? false);
        setBinauralVolume(foundSession.audio?.binaural?.volume ?? 0.2);
        setBaseFreq(foundSession.audio?.binaural?.baseFreq ?? 100);
        setBeatFreq(foundSession.audio?.binaural?.beatFreq ?? 10);

        setVoiceVolume(foundSession.audio?.voice?.volume ?? 1);
    }
    // Si user est connecté mais profileLoading est true (le profil n'est pas encore chargé), on attend.
    // pageLoading restera true et l'UI affichera le loader.

    // Gérer les notes de journal
    const notesRaw = localStorage.getItem('piknowkyo_journal');
    const allJournalEntries: Record<string, string[]> = notesRaw ? JSON.parse(notesRaw) : {};
    setJournalNotes(allJournalEntries[sessionId] || []);

    setPageLoading(false); // Fin du chargement de la page
  }, [sessionId, user, profileLoading, sessions, sessionsLoading, userProfile, appLang, t]);

  // Sauvegarder les overrides audio dans le localStorage (pour cette session spécifique)
  useEffect(() => {
    if (!sessionId || pageLoading) return;

    const currentAudioOverrides = {
      enableMusic, musicVolume, musicFileUrl,
      enableAmbient, ambientVolume, ambientFileUrl,
      enableBinaural, binauralVolume, baseFreq, beatFreq,
      voiceVolume, /* voiceGender, */
      enableDND
    };
    localStorage.setItem(`session_audio_overrides_${sessionId}`, JSON.stringify(currentAudioOverrides));

    // Si ces paramètres audio doivent être des préférences globales de l'utilisateur,
    // dispatch un updateUserProfile ici
    if (user && user.uid && userProfile) { // Added user.uid check
        const updatedAudioConfig: AudioConfig = {
            enableMusic: enableMusic,
            music: { volume: musicVolume, url: musicFileUrl },
            enableAmbient: enableAmbient,
            ambient: { volume: ambientVolume, url: ambientFileUrl },
            enableBinaural: enableBinaural,
            binaural: { volume: binauralVolume, baseFreq: baseFreq, beatFreq: beatFreq },
            voice: {
                volume: voiceVolume,
                language: userProfile.preferences.audioConfig.voice?.language ?? 'fr',
                provider: userProfile.preferences.audioConfig.voice?.provider ?? 'browser',
                voice: userProfile.preferences.audioConfig.voice?.voice ?? 'auto',
            },
        };

        dispatch(updateUserProfile({
            userId: user.uid,
            data: {
                preferences: {
                    ...userProfile.preferences,
                    audioConfig: updatedAudioConfig,
                }
            }
        }));
    }
  }, [
    sessionId, pageLoading, user, dispatch, userProfile,
    enableMusic, musicVolume, musicFileUrl,
    enableAmbient, ambientVolume, ambientFileUrl,
    enableBinaural, binauralVolume, baseFreq, beatFreq,
    voiceVolume, enableDND
  ]);


  // Handler pour les changements provenant de AudioConfigPanel
  const handleAudioConfigChange = (newConfig: AudioConfig) => {
    setEnableMusic(newConfig.enableMusic ?? false);
    setMusicVolume(newConfig.music?.volume ?? 0.5);
    setMusicFileUrl(newConfig.music?.url ?? '');

    setEnableAmbient(newConfig.enableAmbient ?? false);
    setAmbientVolume(newConfig.ambient?.volume ?? 0.3);
    setAmbientFileUrl(newConfig.ambient?.url ?? '');

    setEnableBinaural(newConfig.enableBinaural ?? false);
    setBinauralVolume(newConfig.binaural?.volume ?? 0.2);
    setBaseFreq(newConfig.binaural?.baseFreq ?? 100);
    setBeatFreq(newConfig.binaural?.beatFreq ?? 10);

    setVoiceVolume(newConfig.voice?.volume ?? 1);
    // if (newConfig.voice?.gender) setVoiceGender(newConfig.voice.gender);
  };

  const handlePlaySession = () => {
    if (session) {
      navigate(`/player/${session.id}`);
    }
  };

  const handleSaveJournalNote = (note: string) => {
    if (!session || !sessionId) return;
    const newNotes = [note, ...journalNotes];
    setJournalNotes(newNotes);
    const notesRaw = localStorage.getItem('piknowkyo_journal');
    const allJournalEntries: Record<string, string[]> = notesRaw ? JSON.parse(notesRaw) : {};
    allJournalEntries[sessionId] = newNotes;
    localStorage.setItem('piknowkyo_journal', JSON.stringify(allJournalEntries));
  };

  const benefitsArray: string[] = useMemo(() => {
    return session?.benefits || [];
  }, [session]);

  const handleToggleDND = async () => {
    const newState = !enableDND;
    setEnableDND(newState);

    if (newState) {
      if (notificationPermissionStatus === 'default') {
        const permission = await requestNotificationPermission(t);
        setNotificationPermissionStatus(permission);
        if (permission === 'denied') {
          alert(t('sessionDetails.dnd.permissionDeniedAlert'));
        } else if (permission === 'granted') {
          console.log("Mode NPD (simulation Web) activé. Les notifications de l'application seront bloquées.");
        }
      } else if (notificationPermissionStatus === 'granted') {
        console.log("Mode NPD (simulation Web) activé. Les notifications de l'application seront bloquées.");
      } else if (notificationPermissionStatus === 'denied') {
         alert(t('sessionDetails.dnd.permissionDeniedInfo'));
      }
    } else {
      console.log("Mode NPD (simulation Web) désactivé.");
    }
  };

  // Conditions de chargement et d'erreur pour l'UI principale de SessionDetailPage
  if (pageLoading || sessionsLoading || (user && profileLoading)) {
    return <LoadingContainer><FiLoader />{t('loading.session')}</LoadingContainer>;
  }

  // Si une erreur locale ou Redux s'est produite
  if (localError || sessionsError || profileError) {
    return (
      <PageContainer>
        <BackButton onClick={() => navigate(-1)}><FiChevronLeft /></BackButton>
        <ErrorMessageStyled>{localError || sessionsError || profileError}</ErrorMessageStyled>
      </PageContainer>
    );
  }

  // Si la session n'a pas été trouvée après tout le chargement
  if (!session) {
    return (
      <PageContainer>
        <BackButton onClick={() => navigate(-1)}><FiChevronLeft /></BackButton>
        <ErrorMessageStyled>{t('errors.sessionNotFound')}</ErrorMessageStyled>
      </PageContainer>
    );
  }

  // Déterminer le type d'accès pour l'affichage (similaire à PlayerPage)
  useEffect(() => {
    if (!session) { // Attendre que la session soit chargée
        setAccessType('locked'); // Par défaut si session manquante
        return;
    }
    const isSessionFreeCategory = session.category === 'meditation' || session.category === 'story';
    let isPremiumUser = false;
    let isAdFreeActive = false;

    // Ces flags ne sont pertinents que si l'utilisateur est connecté et le profil est chargé
    if (user && userProfile) {
      isPremiumUser = userProfile.preferences.subscriptions.active && userProfile.preferences.subscriptions.tier === 'premium';
      isAdFreeActive = userProfile.preferences.adWatchData.adFreeUntil ? new Date(userProfile.preferences.adWatchData.adFreeUntil).getTime() > Date.now() : false;
    }

    if (isSessionFreeCategory) {
      setAccessType('free');
    } else if (isPremiumUser) {
      setAccessType('premium');
    } else if (isAdFreeActive) {
      setAccessType('ad-supported');
    } else {
      setAccessType('locked');
    }
  }, [session, user, userProfile]); // Dépend de session, user (pour savoir si connecté), et userProfile (si connecté)


  // Construction de initialConfig pour AudioConfigPanel
  const audioPanelInitialConfig: AudioConfig = {
    enableMusic,
    music: { volume: musicVolume, url: musicFileUrl || session.audio?.music?.url || '' },
    enableAmbient,
    ambient: { volume: ambientVolume, url: ambientFileUrl || session.audio?.ambient?.url || '' },
    enableBinaural,
    binaural: { volume: binauralVolume, baseFreq, beatFreq },
    voice: {
        volume: voiceVolume,
        language: userProfile?.preferences.audioConfig.voice?.language || 'fr',
        provider: userProfile?.preferences.audioConfig.voice?.provider || 'browser',
        voice: userProfile?.preferences.audioConfig.voice?.voice || 'auto',
    },
  };

  return (
    <PageContainer>
      <BackButton onClick={() => navigate(-1)} title={t('actions.back')}>
        <FiChevronLeft />
      </BackButton>

      <HeaderImage imageUrl={session.imageUrl}>
        <HeaderContent>
          <h1>{session.title}</h1>
          <SessionInfoBar>
            <span><FiClock /> {session.duration / 60} {t('units.minutes')}</span>
            <span><FiHeart /> {t(`category.${session.category}`)}</span>
            {session.rating != null && <span><FiStar style={{color: '#FFC107'}}/> {session.rating.toFixed(1)}</span>}
          </SessionInfoBar>
        </HeaderContent>
      </HeaderImage>

      <ContentWrapper>
        <Section>
          <h2><FiInfo /> {t('sessionDetails.description')}</h2>
          <p>{session.description}</p>
        </Section>

        {benefitsArray.length > 0 && (
          <Section>
            <h2><FiStar /> {t('sessionDetails.expectedBenefits')}</h2>
            <ul>
              {benefitsArray.map((benefit: string, index: number) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </Section>
        )}

        {(session.tags && session.tags.length > 0) && (
          <Section>
            <h2><FiTag /> {t('sessionDetails.keywords')}</h2>
            <TagsContainer>
              {(session.tags || []).map((tag: string, index: number) => (
                <span key={index}>#{tag}</span>
              ))}
            </TagsContainer>
          </Section>
        )}

        <Section>
          <h2><FiSettings /> {t('sessionDetails.audioConfigGlobal')}</h2>

          {/* Section "Ne Pas Déranger" - Conservée ici */}
          <AudioToggleGroup>
            <label htmlFor="toggle-dnd">
              {enableDND ? <FiBellOff /> : <FiBell />}
              {t('sessionDetails.dnd.label')}
            </label>
            <div className="toggle-switch">
              <input id="toggle-dnd" type="checkbox" checked={enableDND} onChange={handleToggleDND} />
              <span className="slider"></span>
            </div>
          </AudioToggleGroup>
          {enableDND && notificationPermissionStatus === 'default' && (
            <InfoTextSmall>{t('sessionDetails.dnd.permissionNeededInfo')}</InfoTextSmall>
          )}
          {enableDND && notificationPermissionStatus === 'denied' && (
            <InfoTextSmall>{t('sessionDetails.dnd.permissionDeniedWarning')}</InfoTextSmall>
          )}
          {/* Fin de la Section "Ne Pas Déranger" */}

          {/* Intégration de AudioConfigPanel */}
          {!pageLoading && session && (user && userProfile ? userProfile : true) && ( // Rendre si non loading et session et (user ou profil chargé)
            <AudioConfigPanel
              initialConfig={audioPanelInitialConfig}
              onConfigChange={handleAudioConfigChange}
            />
          )}
        </Section>

        {(session.comments && session.comments.length > 0) && (
          <Section>
            <h2><FiMessageSquare /> {t('sessionDetails.userReviews')}</h2>
            <CommentList>
              {(session.comments || []).map((comment: string, index: number) => (
                <li key={index}>{comment}</li>
              ))}
            </CommentList>
          </Section>
        )}

        {sessionId && (
            <Section id="journal">
                <h2><FiEdit3 /> {t('sessionDetails.yourNotes')}</h2>
                <JournalEntryForm sessionId={sessionId} onSave={handleSaveJournalNote} />
                {journalNotes.length > 0 && (
                <div style={{marginTop: '1rem'}}>
                    <h4>{t('sessionDetails.previousNotes')}:</h4>
                    <CommentList>
                    {journalNotes.map((note: string, index: number) => (
                        <li key={index}>{note}</li>
                    ))}
                    </CommentList>
                </div>
                )}
            </Section>
        )}
      </ContentWrapper>

      {session && ( // Afficher le bouton flottant de lecture
        <PlayButtonFloating onClick={handlePlaySession} title={t('actions.startSession')} disabled={accessType === 'locked'}>
          <FiPlayCircle />
        </PlayButtonFloating>
      )}

      {/* Affichage de la logique d'accès sous le bouton de lecture (exemple, adaptez l'UI) */}
      {session && accessType === 'locked' && (
          <GoToMonetizationButton onClick={() => navigate('/monetization')} style={{marginTop: '1rem'}}>
            {user ? (
                <><FiZap /> {t('detail_page.more_info_button')}</>
            ) : (
                <><FiUser /> {t('auth.login.button')} / {t('auth.signup.button')}</>
            )}
          </GoToMonetizationButton>
      )}
    </PageContainer>
  );
};

export default SessionDetailPage;