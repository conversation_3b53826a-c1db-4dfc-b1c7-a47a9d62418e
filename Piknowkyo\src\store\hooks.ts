// src/store/hooks.ts
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import { createSelector } from 'reselect';
import type { RootState, AppDispatch } from './index';

// Hooks typés pour Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Sélecteurs d'entrée (input selectors)
const selectAuthState = (state: RootState) => state.auth;
const selectSessionsState = (state: RootState) => state.sessions;
const selectJournalState = (state: RootState) => state.journal;
const selectAudioAssetsState = (state: RootState) => state.audioAssets;
const selectSyncState = (state: RootState) => state.sync;
const selectNetworkState = (state: RootState) => state.network;
const selectUserProfileState = (state: RootState) => state.userProfile;
const selectPricingState = (state: RootState) => state.pricing; // <-- AJOUTEZ CE SÉLECTEUR

// Sélecteurs mémoïsés pour chaque slice
export const selectAuth = createSelector(
  [selectAuthState],
  (auth) => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    loading: auth.isLoading, // <-- ASSUREZ-VOUS QUE C'EST auth.isLoading ICI
    error: auth.error,
    lastSyncTimestamp: auth.lastSyncTimestamp,
  })
);

export const selectSessions = createSelector(
  [selectSessionsState],
  (sessions) => ({
    sessions: sessions.sessions,
    currentSession: sessions.currentSession,
    loading: sessions.loading,
    error: sessions.error,
    lastSyncTimestamp: sessions.lastSyncTimestamp,
    pendingChanges: sessions.pendingChanges,
  })
);

export const selectJournal = createSelector(
  [selectJournalState],
  (journal) => ({
    entries: journal.entries,
    loading: journal.loading,
    error: journal.error,
    lastSyncTimestamp: journal.lastSyncTimestamp,
    pendingChanges: journal.pendingChanges,
  })
);

export const selectAudioAssets = createSelector(
  [selectAudioAssetsState],
  (audioAssets) => ({
    assets: audioAssets.assets,
    manifest: audioAssets.manifest,
    loading: audioAssets.isLoading,
    error: audioAssets.error,
    lastSyncTimestamp: audioAssets.lastSyncTimestamp,
    pendingChanges: audioAssets.pendingChanges,
  })
);

export const selectSync = createSelector(
  [selectSyncState],
  (sync) => ({
    isOnline: sync.isOnline,
    isSyncing: sync.isSyncing,
    lastSyncTimestamp: sync.lastSyncTimestamp,
    syncErrors: sync.syncErrors,
    pendingSyncCount: sync.pendingSyncCount,
    syncStatus: sync.syncStatus,
    conflicts: sync.conflictResolution.conflicts,
    conflictStrategy: sync.conflictResolution.strategy,
  })
);

export const selectNetwork = createSelector(
  [selectNetworkState],
  (network) => ({
    isOnline: network.isOnline,
    connectionType: network.connectionType,
    isSlowConnection: network.isSlowConnection,
    lastOnlineTimestamp: network.lastOnlineTimestamp,
    offlineDuration: network.offlineDuration,
  })
);

// Sélecteur mémoïsé pour le profil utilisateur
export const selectUserProfile = createSelector(
  [selectUserProfileState],
  (userProfile) => ({
    profile: userProfile.profile,
    loading: userProfile.loading,
    saving: userProfile.saving,
    error: userProfile.error,
  })
);

// Sélecteur mémoïsé pour les prix (ajouté)
export const selectPricing = createSelector( // <-- NOUVEAU SÉLECTEUR
  [selectPricingState],
  (pricing) => ({
    premiumConfig: pricing.premiumConfig,
    pricingPlans: pricing.pricingPlans,
    isLoading: pricing.isLoading,
    error: pricing.error,
  })
);


// Sélecteur mémoïsé pour l'état hors ligne (était déjà là, c'est un sélecteur valide)
export const selectOfflineStatus = createSelector(
  [selectNetwork, selectSync],
  (network, sync) => ({
    isOffline: !network.isOnline,
    hasPendingChanges: sync.pendingSyncCount > 0,
    canSync: network.isOnline && !sync.isSyncing,
    syncStatus: sync.syncStatus,
    offlineDuration: network.offlineDuration,
  })
);


// Hooks composés pour des cas d'usage spécifiques (utiliser les sélecteurs mémoïsés)
export const useAuth = () => useAppSelector(selectAuth);
export const useSessions = () => useAppSelector(selectSessions);
export const useJournal = () => useAppSelector(selectJournal);
export const useAudioAssets = () => useAppSelector(selectAudioAssets);
export const useSync = () => useAppSelector(selectSync);
export const useNetwork = () => useAppSelector(selectNetwork);
export const useUserProfile = () => useAppSelector(selectUserProfile);
export const usePricing = () => useAppSelector(selectPricing); // <-- NOUVEAU HOOK POUR LES PRIX

// Correction : Créez un hook pour useOfflineStatus en l'enveloppant dans useAppSelector
export const useOfflineStatusHook = () => useAppSelector(selectOfflineStatus); // <-- CORRECTION ICI : NOUVEAU HOOK

// Si vous utilisez `useOfflineStatus` directement dans `ReduxExample.tsx` comme un hook,
// vous devez soit renommer `selectOfflineStatus` en `useOfflineStatus` et le rendre un hook
// soit utiliser un nom différent comme `useOfflineStatusHook`
// Pour éviter la confusion, je recommande d'utiliser le nouveau `useOfflineStatusHook`.