// src/services/authService.ts
import { User, onAuthStateChanged } from 'firebase/auth'; // Importez le type User de Firebase
import { auth } from '../firebase'; // Importez l'instance 'auth' de votre fichier firebase.ts consolidé
import { store } from '../store/index'; // Importez votre store Redux
// Importez toutes les actions nécessaires de votre authSlice
import { setAuthUser, setAuthLoading, setAuthError, updateLastSyncTimestamp, clearAuth } from '../store/slices/authSlice';
// Les imports suivants sont généralement gérés par App.tsx ou d'autres composants
// import { fetchUserProfile } from '../store/slices/userProfileSlice';
// import { performFullSync } from '../store/slices/syncSlice';
// import { SyncService } from './syncService';

export class AuthService {
  private static unsubscribe: (() => void) | null = null;

  static initialize() {
    // Si un abonnement existe déjà, le nettoyer pour éviter les multiples écouteurs
    if (this.unsubscribe) {
      this.unsubscribe();
    }

    // Définir l'état de chargement de l'authentification à true au démarrage de l'app
    store.dispatch(setAuthLoading(true));

    // Obtenir la fonction dispatch ici, accessible à toute la portée de initialize
    const dispatch = store.dispatch;

    // --- C'EST LA LIGNE D'ERREUR PRÉCÉDENTE (authService.ts:29) : Maintenant corrigée par la portée de 'dispatch' et la transformation dans authSlice ---
    this.unsubscribe = onAuthStateChanged(auth, async (firebaseUser: User | null) => {
      // 'dispatch' est maintenant accessible ici

      if (firebaseUser) {
        console.log('User signed in:', firebaseUser.email);
        // Dispatch l'objet Firebase User direct. Le reducer `setAuthUser` se chargera de la sérialisation.
        dispatch(setAuthUser(firebaseUser));

        // Note: Le fetch du profil utilisateur et la sync auto sont maintenant gérés dans App.tsx
        // ou des composants spécifiques pour mieux contrôler quand ces fetches se produisent.
        // Ce service se concentre sur l'état d'authentification.
      } else {
        console.log('User signed out');
        // Dispatch null si l'utilisateur est déconnecté, `setAuthUser` nettoie l'état sérialisable.
        dispatch(setAuthUser(null));
        // Optionnel: nettoyer tout l'état Redux lié à l'utilisateur après déconnexion
        // dispatch(clearAuth()); // Utilisez cette action si vous voulez effacer toutes les données user-specific
      }
      // Le chargement est marqué comme terminé par `setAuthUser` ou `setAuthError`
    }, (error) => {
      // 'dispatch' est maintenant accessible ici aussi
      console.error('AuthService Firebase Auth Error:', error);
      // En cas d'erreur grave (ex: problème réseau, jeton invalide), s'assurer que l'utilisateur est déconnecté localement
      dispatch(setAuthError(error.message));
      dispatch(setAuthUser(null)); // S'assurer que l'état utilisateur est effacé en cas d'erreur d'auth
    });
  }

  // Nettoyage de l'abonnement onAuthStateChanged
  static cleanup() {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
      // Optionnel : Effacer l'état d'authentification Redux lors du cleanup du service
      // store.dispatch(clearAuth());
    }
  }
}