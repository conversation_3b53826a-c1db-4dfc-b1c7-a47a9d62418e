// src/models.ts (VERSION COMPLÈTE ET CORRIGÉE)
import { Language } from './LangProvider';

// Configuration audio d'une session
export interface SessionAudioConfig {
  music?: {
    url: string;
    volume?: number;
  };
  ambient?: {
    url: string;
    volume?: number;
  };
  binaural?: {
    volume?: number; // Volume commun
    baseFreq?: number;  // Fréquence de base (ex: pour l'oreille gauche)
    beatFreq?: number;  // Fréquence du battement désiré
  };
  voice?: {
    volume?: number;
    gender?: 'masculine' | 'feminine' | 'neutral';
  };
  enableMusic?: boolean;
  enableAmbient?: boolean;
  enableBinaural?: boolean;
}

// Actifs audio (musique, ambiance)
export interface AudioAsset {
  id: string;
  name: string;
  url: string;
  type: 'music' | 'ambient'; // Types spécifiques si vous les gérez
  isUserUploaded?: boolean;
  userId?: string;
  storagePath?: string;
  size?: number;
  createdAt?: Date;
}

// Li<PERSON> de script pour une séance vocale
export interface ScriptLine {
  text: string;
  duration?: number; // Durée de la ligne en secondes (temps de parole)
  pause?: number;    // Pause après la ligne en secondes
  speaker?: string;  // Nom de l'orateur si plusieurs voix
  rate?: number;     // Vitesse de lecture
  pitch?: number;    // Tonalité de la voix
}

// Interface principale pour une Session
export interface Session {
  id: string;
  title: string;
  description: string;
  audioUrl: string; // URL du fichier audio principal de la session
  duration: number; // Durée totale de la session en SECONDES (si c'est ce que votre slice attend)
  type: string; // Ex: 'meditation', 'story'. Devrait être dynamique.
  category: string; // Ex: 'relaxation', 'focus'. Devrait être dynamique.
  language: Language;
  imageUrl?: string;
  // Note: Si `durationMinutes` et `estimatedDuration` sont des champs distincts pour l'affichage,
  // et que `duration` est la durée exacte en secondes pour le lecteur, c'est bon.
  durationMinutes?: number; // Durée en minutes (peut être calculée à partir de `duration`)
  estimatedDuration?: number; // Durée estimée en minutes (pour l'affichage rapide)
  rating?: number; // Note moyenne
  tags?: string[]; // Mots-clés
  benefits?: string[]; // Bénéfices de la session
  comments?: string[]; // IDs des commentaires
  audio?: SessionAudioConfig; // Configuration audio détaillée
  script?: ScriptLine[]; // Script textuel pour la session
  // Ajoutez d'autres propriétés ici si elles sont présentes dans vos données Firestore/JSON
}

// Manifeste audio (pour regrouper musiques et ambiances)
export interface AudioManifest {
  musics: AudioAsset[];
  ambiants: AudioAsset[];
}

// Entrée de journal
export type JournalEntry = {
  id: string;
  sessionId: string;
  date: string;
  note: string;
  mood: 'happy' | 'neutral' | 'sad' | 'energized' | 'relaxed';
};

// Entrée de manifeste de session (pour les listes/écrans de sélection)
export interface SessionManifestEntry {
  id: string;
  title: string;
  type: string;
  estimatedDuration: number;
  tags: string[];
  imageUrl?: string;
  isPremium?: boolean;
}