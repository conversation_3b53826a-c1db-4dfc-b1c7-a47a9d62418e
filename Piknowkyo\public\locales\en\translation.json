{"navigation": {"home": "Home", "sessions": "Sessions", "games": "Games", "journal": "Journal", "stats": "Statistics", "leaderboard": "Leaderboard", "blog": "Blog", "profile": "Profile", "monetization": "Premium", "settings": "Settings", "about": "About"}, "common": {"welcome": "Welcome to PiKnowKyo", "ok": "OK", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "loading": "Loading...", "error": "Error", "success": "Success"}, "home": {"title": "Welcome to PiKnowKyo", "subtitle": "Your personal wellness companion. Discover guided sessions, track your progress and cultivate your inner growth.", "exploreButton": "Explore Sessions", "welcomeText": "Start your journey towards sustainable well-being with our personalized tools and caring community.", "quickAccess": "Quick Access", "learnMore": "Learn more about PiKnowKyo"}, "sessions": {"title": "Wellness Sessions", "description": "Discover our collection of guided sessions for your personal development.", "searchPlaceholder": "Search for a session...", "allTypes": "All types", "allDurations": "All durations", "durationLabel": "Duration", "type": "Type", "category": "Category", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noSessionsFound": "No sessions found matching your criteria.", "clearFilters": "Clear filters", "filters": "Filters", "filterBy": "Filter by", "sessionType": "Session type", "gridView": "Grid View", "listView": "List View", "noResultsMatchCriteria": "No sessions match your criteria.", "noSessionsAvailable": "No sessions available at the moment.", "meditation": "Meditation", "hypnosis": "Hypnosis", "affirmations": "Affirmations", "custom": "Custom", "viewModes": {"grid": "Grid", "list": "List"}, "duration": {"label": "Duration", "under15": "Under 15 min", "15to30": "15 - 30 min", "over30": "Over 30 min"}}, "games": {"title": "Personal Development Mini-Games", "intro": "Test and improve your skills with our fun and challenging mini-games.", "estimatedDuration": "Estimated duration", "personalBest": "Personal best", "continueGame": "Continue game", "newGame": "New game", "gameInfo": "Game information", "gameRules": "Game rules", "gameOver": "Game over", "finalScore": "Final score", "newRecord": "New record!", "playAgain": "Play again", "backToGames": "Back to games", "pause": "Pause", "resume": "Resume", "quit": "Quit", "maxLevels": "This game contains {{maxLevels}} difficulty levels.", "yourBestScore": "Your best score on this game is {{score}} points.", "keywords": "Keywords", "gameOverSummary": "Congratulations! Your final score is {{score}} points and you reached level {{level}} in {{time}} seconds.", "zenTetris": {"title": "Zen Tetris", "description": "A relaxing version of the famous block game. Improve your concentration and stress management.", "rules": "Place falling pieces to complete horizontal lines. The more lines you clear at once, the more points you earn. The game gradually speeds up.", "controls": "Touch controls", "tips": "Stay calm, plan your moves, and try to create combos to maximize your score.", "touchTap": "Quick tap: Rotation", "touchLeft": "Swipe left: Move left", "touchRight": "Swipe right: Move right", "touchDown": "Swipe down: Soft drop", "touchButtons": "Control buttons at bottom"}}, "game": {"pauseButton": "Pause", "info": "Info", "level": "Level", "lines": "Lines", "score": "Score", "time": "Time", "nextPiece": "Next Piece", "moveLeft": "Move left", "moveRight": "Move right", "rotate": "Rotate", "softDrop": "Soft drop", "modal": {"rulesTitle": "Game Rules", "pausedTitle": "Game Paused", "gameOverTitle": "Game Over!", "pausedMessage": "Your game is paused. Resume when you're ready.", "gameOverMessage": "Well played! Your final score is {{score}} and you reached level {{level}}.", "return": "Return", "restart": "<PERSON><PERSON>", "resume": "Resume", "start": "Start"}, "controls": {"keyboard": "Keyboard", "touch": "Touch"}, "zenTetris": {"rules1": "Stack blocks to form complete lines and score points. Speed increases with levels!", "rules2": "Controls:", "ruleMoveLeft": "Move left", "ruleMoveRight": "Move right", "ruleSoftDrop": "Soft drop", "ruleRotate": "Rotate", "rulePause": "Pause"}}, "journal": {"title": "Tracking Journal", "description": "Find here all your personal notes, organized by session. Reflect on your experiences and track your progress.", "noNotesYet": "Your journal is still empty.", "startSessionPrompt": "Start a session and take notes to see your reflections here.", "unknownSession": "Session (ID: {{id}})", "notesPlural": "notes", "noteSingular": "note", "seeAllNotes": "See all {{count}} notes...", "trackingJournal": "Tracking Journal", "addEntry": "Add your reflection on this session..."}, "stats": {"title": "Your Wellness Statistics", "description": "Track your journey, celebrate your progress and discover your trends.", "sessionsFollowed": "Sessions Practiced", "sessionsFollowedDesc": "Number of unique sessions with notes.", "totalTime": "Total Session Time", "totalTimeDesc": "Estimated cumulative time.", "favoriteSession": "Favorite Session", "favoriteSessionDesc": "The highest rated.", "notesWritten": "Total Notes Written", "notesWrittenDesc": "Number of reflections recorded.", "typesFollowed": "Distribution by Session Type", "timePerSession": "Detail by Session (Estimated)", "noTypesYet": "No specific session type followed yet.", "noTimePerSession": "No time data per session available.", "timesPlural": "times", "timesSingular": "time", "notesPlural": "notes", "noteSingular": "note", "duration": {"min": "min", "h": "h"}}, "blog": {"title": "Community Journal", "description": "Share your experiences, discoveries and inspirations with the PiKnowKyo community. All messages are anonymous.", "searchPlaceholder": "Search messages...", "allCategories": "All categories", "writeNewPost": "Write a new message", "postPlaceholder": "Your message (will be published anonymously)...", "category": "Category", "publishing": "Publishing...", "publish": "Publish", "loginToPost": "You must be logged in to publish a message.", "noPostsYet": "No messages yet in this category or matching your search.", "like": "Like", "comments": "Comments", "addComment": "Add a comment", "commentPlaceholder": "Your comment (anonymous)...", "postComment": "Post comment", "noCommentsYet": "No comments yet. Be the first to comment!", "backToBlog": "Back to blog", "postNotFound": "Post not found", "commentsSectionTitle": "Comments", "yourCommentPlaceholder": "Your comment...", "sending": "Sending...", "sendComment": "Send", "loginToComment": "Log in to add a comment.", "sampleAuthor": "Anonymous Author", "samplePostContent": "Detailed message content. This message talks about the importance of mindfulness in our stressful daily lives and how simple exercises can bring great inner peace.", "sampleCommenter1": "Commenter1", "sampleCommenter2": "<PERSON><PERSON><PERSON>", "sampleComment1": "Great message!", "sampleComment2": "Very interesting, thanks for sharing.", "anonymousUser": "Anonymous User", "categories": {"général": "General", "gratitude": "Gratitude", "défis": "Challenges", "inspirations": "Inspirations", "questions": "Questions"}}, "about": {"title": "About", "description": "Discover <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, your companion for personal growth and well-being.", "philosophy": {"title": "Our Philosophy: The Journey from Pi to Kyo", "pi": {"title": "Pi (π)", "description": "The infinite, the sacred mystery of the universe and the fundamental harmony that unites us all. It's the starting point, the opening to the unknown."}, "know": {"title": "Know", "description": "Exploration, structured learning and mental clarity. It's the acquisition of tools and understanding to navigate the path."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "Teaching, embodied wisdom, illumination and altruistic sharing of discovered light. It's the culmination and radiance."}, "conclusion": "PiKnowKyo is more than an application, it's a compass for your inner growth, inspired by"}, "tools": {"title": "Our Tools for Your Growth", "description": "We offer a varied range of sessions and tools designed to accompany you on your personal growth journey:", "hypnosis": "Evolutionary Hypnosis to explore your subconscious and initiate profound changes.", "meditation": "Guided Meditations to cultivate mindfulness, inner peace and emotional resilience.", "affirmations": "Positive Affirmations to reprogram your thoughts and strengthen your self-confidence.", "nlp": "NLP Training (Neuro-Linguistic Programming) to improve your communication and achieve your goals.", "stories": "Metaphorical Stories to stimulate your imagination and facilitate integration of new perspectives."}, "experience": {"title": "A Holistic Experience Designed for You", "audio": "100% configurable audio (music, ambiance, voice, binaural).", "guidedPaths": "Guided paths and custom session creation.", "community": "Caring community and anonymous ranking (optional).", "blog": "Internal blog with articles, tips and inspiring resources.", "multilang": "Multi-language support and customizable light/dark themes.", "notifications": "Gentle motivational notifications to accompany you."}, "features": {"customizable": "Fully customizable sessions according to your needs.", "journal": "Personal journal to track your progress and reflections.", "stats": "Detailed statistics to visualize your evolution."}, "monetization": {"title": "Ethical Monetization:", "description": "We offer a free trial, optional subscription for full access, minimal and non-intrusive ads (bypassable with subscription), and the possibility of donations to support our mission."}, "community": {"title": "Join Our Community", "description": "PiKnowKyo is designed for those who value self-knowledge, organization of their thoughts and personal productivity with a focus on well-being. Whether you're a student, professional, researcher, or simply a curious mind seeking harmony, our application is your ally.", "contact": "For any questions, suggestions or if you need assistance, please don't hesitate to send us an email at:", "website": "You can also visit our website", "moreInfo": "for more information"}}, "settings": {"title": "Settings", "audio": "Audio", "language": "Language", "voice": "Voice", "autoVoice": "Auto voice", "testVoice": "Test voice", "saveConfig": "Save configuration", "ttsSectionTitle": "Text-to-Speech (TTS)", "ttsProvider": "TTS Provider", "ttsTestText": "This is a text-to-speech test.", "ttsTestError": "Error testing voice", "downloadingVoice": "Downloading voice...", "voiceDownloaded": "Voice downloaded", "noVoiceForSelection": "No voice available for this selection", "noSpecificVoiceForLang": "No specific voice for this language. Here are all available voices:", "explanationsTitle": "Explanations", "audioAssetsManagementTitle": "Audio files management", "audioAssetsInfo": "Manage your custom music and ambient sounds", "goToAudioAssets": "Manage audio files", "providerLabels": {"browser": "Browser", "piper": "<PERSON> (AI)"}, "ttsProviderInfo": {"browser": "Uses browser built-in voices", "piper": "High-quality AI voices (requires download)"}, "modal": {"saveSuccessTitle": "Configuration saved", "saveSuccessMessage": "Your settings have been saved successfully", "testErrorTitle": "Test error"}}, "monetization": {"title": "Our Plans & Support", "description": "Choose the plan that suits you or support our mission to continue offering accessible wellness tools.", "currentPlan": "Your Current Plan: Premium", "subCancelsOn": "Your subscription will expire on", "subRenewsOn": "Next renewal on", "manageSub": "Manage my subscription", "donateTitle": "Support PiKnowKyo", "donateDescription": "Your generosity helps us maintain and improve the application for everyone. Every contribution counts!", "donateButton": "Make a donation", "donateAlert": "Thank you for your interest! The donation feature will be available soon. In the meantime, you can visit our website.", "manageSubAlert": "Simulation: Redirecting to subscription management portal. You could cancel or update your subscription here."}, "sessionDetails": {"description": "Description", "expectedBenefits": "Expected Benefits", "keywords": "Keywords", "startSession": "Start Session", "backToSessions": "Back to Sessions", "audioConfigGlobal": "Session Audio Configuration", "userReviews": "User Reviews", "yourNotes": "Your Journal for this Session", "previousNotes": "Previous notes", "dnd": {"label": "Do Not Disturb (Application)", "permissionNeededInfo": "<PERSON><PERSON><PERSON> will request notification permission to optimize this mode.", "permissionDeniedWarning": "Notification permission denied. Application DND mode is active, but system notifications are not affected."}}, "units": {"minutes": "min", "points": "pts", "seconds": "sec"}, "actions": {"back": "Back", "backToBlog": "Back to blog", "backToHome": "Back to home", "backToSessionDetails": "Back to details", "backToSessions": "Back to sessions", "backToSettings": "Back to settings", "cancel": "Cancel", "delete": "Delete", "deleteConfirm": "Confirm deletion", "deleting": "Deleting...", "enterFullscreen": "Enter fullscreen", "exitFullscreen": "Exit fullscreen", "ok": "OK", "pause": "Pause", "play": "Play", "preview": "Preview", "restart": "<PERSON><PERSON>", "startSession": "Start session", "stopPreview": "Stop", "stopTest": "Stop test", "testSound": "Test sound", "testVoice": "Test voice", "upload": "Upload"}, "menu": {"navigation": "Navigation", "account": "Account"}, "notFound": {"message": "Sorry, the page you are looking for does not exist.", "backHome": "Back to home"}, "quiz": {"title": "Quiz", "description": "Select a quiz to start testing your knowledge!", "comingSoon": "Quizzes coming soon! Stay tuned."}, "history": {"title": "Historial de actividades", "description": "Encuentra aquí todas tus actividades pasadas: sesiones de meditación, juegos, ejercicios...", "comingSoon": "¡El historial estará disponible pronto! Vuelve para ver tus actividades pasadas.", "no_entries": "Aún no tienes actividades registradas. ¡Inicia una sesión para verla aquí!", "loading_entries": "Cargando historial...", "error_loading_entries": "Error al cargar el historial", "entry_type_session": "Sesión", "entry_type_game": "Ju<PERSON>", "entry_type_meditation": "Meditación", "entry_type_exercise": "<PERSON><PERSON><PERSON><PERSON>", "entry_type_custom": "Personalizado", "entry_duration": "Duración: {{duration}}", "entry_minutes_short": "min", "entry_seconds_short": "s", "entry_details": "Detalles: {{details}}"}, "categories": {"title": "Categories", "description": "Choose a category to explore related quizzes.", "comingSoon": "Categories coming soon!"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "This browser does not support notifications."}, "pseudoGenerator": {"adjectives": {"light": "<PERSON>", "wind": "Windy", "ocean": "Oceanic", "mountain": "Mountainous", "star": "Starry", "forest": "Forested", "river": "Riverine", "sun": "Solar", "moon": "Lunar", "aurora": "Boreal", "calm": "Calm", "serene": "<PERSON><PERSON>", "wise": "<PERSON>", "peaceful": "Peaceful", "gentle": "Gentle", "silent": "Silent", "golden": "Golden", "silver": "<PERSON><PERSON>", "deep": "Deep", "vast": "Vast", "subtle": "Subtle", "vibrant": "Vibrant", "fluid": "Fluid", "whispering": "Whispering", "ancient": "Ancient", "young": "<PERSON>", "free": "Free", "open": "Open", "sparkling": "Sparkling", "radiant": "<PERSON><PERSON><PERSON>", "mystic": "Mystic", "ethereal": "Ethereal", "luminous": "Luminous", "tranquil": "Tranquil", "velvety": "<PERSON><PERSON>", "crisp": "<PERSON><PERSON><PERSON>", "harmonious": "Harmonious", "celestial": "Celestial", "gleaming": "Gleaming", "serendipitous": "Serendipitous", "enchanting": "Enchanting", "pristine": "Pristine", "graceful": "Graceful", "infinite": "Infinite", "tender": "<PERSON>der", "radiating": "Radiating", "soothing": "Soothing", "blissful": "Blissful", "majestic": "<PERSON><PERSON><PERSON>", "lively": "Lively", "noble": "<PERSON>", "pure": "Pure", "elegant": "Elegant", "divine": "Divine", "resplendent": "Resplendent", "seraphic": "<PERSON><PERSON><PERSON>", "venerable": "Venerable", "exquisite": "Exquisite", "buoyant": "Buoyant", "zestful": "Zestful", "iridescent": "Iridescent", "poised": "Poised", "delicate": "Delicate", "fervent": "<PERSON><PERSON><PERSON>", "alluring": "Alluring", "spirited": "Spirited", "dazzling": "Dazzling", "tranquilizing": "Tranquilizing", "refined": "Refined", "captivating": "Captivating", "lustrous": "Lu<PERSON><PERSON>", "vivid": "Vivid", "prismatic": "Prismatic", "dynamic": "Dynamic", "charming": "<PERSON><PERSON>", "idyllic": "Idyllic", "melodic": "<PERSON><PERSON><PERSON>", "dreamy": "<PERSON>y", "picturesque": "Picturesque", "twinkling": "Twinkling", "blazing": "Blazing", "sublime": "Sublime"}, "nouns": {"serene": "Serenity", "calm": "Calmness", "wise": "Wisdom", "peaceful": "Peace", "clairvoyant": "Clairvoyance", "harmonious": "Harmony", "awakened": "Awakening", "free": "Freedom", "creative": "Creativity", "intuitive": "Intuition", "spirit": "Spirit", "dreamer": "Dreamer", "seeker": "Seeker", "wanderer": "<PERSON><PERSON><PERSON>", "healer": "He<PERSON>r", "guide": "Guide", "messenger": "<PERSON>", "guardian": "Guardian", "listener": "Listener", "explorer": "Explorer", "observer": "Observer", "anchor": "<PERSON><PERSON>", "beacon": "Beacon", "pillar": "<PERSON><PERSON>", "essence": "Essence", "echo": "Echo", "source": "Source", "haven": "Haven", "path": "Path", "horizon": "Horizon", "breeze": "<PERSON><PERSON>", "flame": "Flame", "sky": "Sky", "dawn": "Dawn", "twilight": "Twilight", "cloud": "Cloud", "stream": "Stream", "meadow": "Meadow", "valley": "Valley", "summit": "Summit", "wave": "Wave", "spark": "Spark", "shadow": "Shadow", "lightning": "Lightning", "thunder": "Thunder", "mist": "Mist", "rainbow": "Rainbow", "glade": "Glade", "tide": "Tide", "oasis": "Oasis", "ridge": "Ridge", "bloom": "Bloom", "ember": "Ember", "glimmer": "Glimmer", "cascade": "Cascade", "zephyr": "<PERSON><PERSON><PERSON><PERSON>", "reef": "Reef", "canyon": "Canyon", "drift": "Drift", "blaze": "Blaze", "vista": "Vista", "current": "Current", "sparkle": "Sparkle", "whirlwind": "Whirlwind", "sanctuary": "Sanctuary", "journey": "Journey", "destiny": "Destiny", "pulse": "Pulse", "radiance": "Radiance", "silence": "Silence", "bounty": "Bounty", "mystery": "Mystery", "legend": "Legend", "vision": "Vision", "dream": "Dream", "hope": "Hope", "gleam": "Gleam", "quest": "Quest", "infinity": "Infinity", "sanctum": "Sanctum", "flow": "Flow", "heart": "Heart"}}, "app": {"name": "Piknowkyo", "theme_light": "Switch to light theme", "theme_dark": "Switch to dark theme", "logo_alt": "Piknowkyo logo"}, "auth": {"common": {"email_placeholder": "Email Address", "password_placeholder": "Password", "or_separator": "OR", "please_wait_loading": "Please wait...", "success_redirect": "Success! Redirecting..."}, "login": {"subtitle": "Welcome back!", "button": "Log In", "button_loading": "Logging in...", "google_button": "Log in with Google", "google_button_loading": "Logging in with Google...", "error_invalid_credentials": "Incorrect email or password.", "error_google_popup_closed": "Google login window was closed. Please try again.", "error_google_popup_cancelled": "A Google popup request is already in progress or was cancelled. Please try again.", "error_general": "<PERSON><PERSON> failed. Please try again.", "toggle_signup": "Don't have an account yet? Sign Up"}, "signup": {"subtitle": "Create your account!", "confirm_password_placeholder": "Confirm Password", "button": "Sign Up", "button_loading": "Signing up...", "google_button": "Sign up with Google", "google_button_loading": "Signing up with Google...", "error_password_mismatch": "Passwords do not match.", "error_email_in_use": "This email is already in use. Please log in.", "error_weak_password": "Password is too weak (minimum 6 characters).", "error_general": "Signup failed. Please try again.", "toggle_login": "Already have an account? Log In"}, "logout": {"button": "Log out", "button_aria_label": "Log out from your account"}}, "preferences": {"language": {"question": "Choose your preferred language"}, "notifications": {"question": "Would you like to receive motivational notifications?"}, "premium": {"question": "Would you like to test premium features for free (with non-intrusive ads)?"}, "yes": "Yes", "no": "No", "thanks": "Thank you!", "validate": "Validate my preferences"}, "questionnaire": {"goal": {"question": "What is your main goal?", "relaxation": "Relaxation", "confidence": "Self-confidence", "stress": "Stress management", "spirituality": "Spirituality", "other": "Other"}, "experience": {"question": "Have you ever practiced hypnosis or meditation?", "never": "Never", "sometimes": "Sometimes", "regularly": "Regularly"}, "audio": {"question": "Do you prefer a session with music, natural sounds, or silence?", "music": "Music", "nature": "Natural sounds", "silence": "Silence"}, "thanks": "Thank you!", "viewSuggestions": "View my suggestions"}, "notificationTest": {"heading": "Notification Test", "platform": "Current platform:", "status": "Web notifications status:", "title": "Notification Test", "body": "This is a test notification from PiKnowKyo", "sendButton": "Send test notification"}, "reduxExample": {"loading": "Loading sessions...", "title": "Redux Example - Sessions", "reduxState": "Redux State:"}, "audioAssets": {"title": "Audio files management", "musicTitle": "Music", "ambientTitle": "Ambient sounds", "noMusics": "No music available", "noAmbiants": "No ambient sounds available", "selectFile": "Select file", "changeFile": "Change file", "uploadMusicPrompt": "Upload new music", "uploadAmbientPrompt": "Upload new ambient sound", "uploading": "Uploading...", "uploadSuccess": "File {{fileName}} uploaded successfully!", "uploadError": "Upload error", "previewError": "Cannot play audio preview", "cannotDeleteDefault": "Default files cannot be deleted", "confirmDeleteTitle": "Confirm deletion", "confirmDeleteMessage": "Are you sure you want to delete this file?", "deleteSuccess": "File deleted successfully", "deleteError": "Deletion error"}, "audioConfig": {"webAudioNotSupported": "Web Audio API not supported by your browser.", "musicTitle": "Background Music", "musicTooltip": "Choose ambient music to accompany your session. You can adjust the volume.", "ambientTitle": "Ambient Sounds", "ambientTooltip": "Add natural or ambient sounds to create the perfect atmosphere.", "binauralBeats": "Binaural / Isochronic Sounds", "binauralTooltip": "Generate sounds to influence brainwaves. Requires headphones for optimal binaural effect.", "ttsTitle": "Voice Synthesis", "ttsTooltip": "Adjust the guide's voice volume. Voice type and language are managed in the application's general settings.", "musicSound": "Music:", "ambientSound": "Ambient sound:", "volume": "Volume", "baseFrequency": "Base frequency (Hz)", "baseFreqPresets": "Base Freq. Presets:", "beatFrequency": "Beat (Hz)", "brainwavePresets": "Brainwave Presets (Beat):", "selectPreset": "-- Choose a preset --", "selectState": "-- Choose a state --", "targetFrequencyInfo": "Left Ear: {{leftEar}} Hz, Right Ear: {{rightEar}} Hz", "headphonesRequired": "Requires headphones for binaural effect"}, "errors": {"missingPostId": "Missing post ID.", "postNotFound": "Post not found.", "cantLoadPost": "Cannot load post.", "cantLoadComments": "Cannot load comments.", "cantAddComment": "Error adding comment.", "userNotAuthenticated": "You must be logged in to perform this action.", "manageSubscriptionError": "Unable to access subscription management.", "paymentError": "Error processing payment.", "cantAddPost": "Error publishing the message.", "cantLoadSessions": "Unable to load session data."}, "player": {"sessionEnded": "Session ended.", "readyToStart": "Ready to start...", "audioSettings": "Volumes", "volumeControls": "Volume Controls", "music": "Music", "ambient": "Ambient", "voice": "Voice", "binaural": "Binaural beats"}, "test": {"newSongTitle": "New song title", "addNewSong": "Add new song"}, "sync": {"offline": "Offline", "syncing": "Syncing...", "error": "Sync error ({{count}})", "pending": "{{count}} pending", "synchronized": "Synchronized", "syncedMinutesAgo": "Synced {{minutes}}min ago", "syncedHoursAgo": "Synced {{hours}}h ago", "online": "Online", "clickToSync": "Click to sync"}, "loading": {"user": "Loading user information...", "profile": "Loading profile...", "blog": "Loading blog...", "comments": "Loading comments...", "post": "Loading post...", "leaderboard": "Loading leaderboard...", "stats": "Loading statistics..."}, "plans": {"free": {"title": "Free Plan", "price": "$0", "currentPlan": "Your Current Plan", "switchToFree": "Switch to Free plan"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Billed monthly, cancel anytime.", "manageSub": "Manage Subscription", "subscribe": "Upgrade to Premium"}, "billing": {"month": "month"}}, "features": {"free": {"baseMeditations": "Access to basic meditations and stories", "backgroundMusic": "Basic background music and TTS voices", "stats": "Progress statistics", "blog": "Access to community blog", "leaderboard": "Anonymous leaderboard participation"}, "premium": {"allSessions": "Unlimited access to ALL sessions (hypnosis, NLP, etc.)", "ambientSounds": "Advanced ambient and binaural sounds", "customSessions": "Create custom sessions", "games": "Access to mindfulness mini-games", "journal": "Detailed tracking journal", "motivationNotifs": "Personalized motivation notifications", "calendar": "Calendar and custom programs (coming soon)", "customAudio": "Use your own sounds and music", "noAds": "Ad-free experience", "prioritySupport": "Priority support"}}, "legal": {"privacy": "Privacy Policy", "terms": "Terms and Conditions"}, "profile": {"title": "My Profile", "notConnectedTitle": "User Profile", "pleaseLogin": "Please log in to access your profile.", "publicPseudo": "Public username", "regeneratePseudo": "Generate new username", "premiumMember": "Premium Member", "manageSubscription": "Manage subscription", "upgradeToPremium": "Upgrade to Premium", "preferencesTitle": "Preferences", "appLanguage": "Application language", "grammaticalGenderLabel": "How would you prefer to be addressed in scripts?", "grammaticalGenderInfo": "This will help us adapt certain texts for a more personalized experience.", "quickActions": "Quick actions", "goToSettings": "Advanced settings", "viewStats": "View my statistics", "accountActionsTitle": "Account Management", "logout": "Logout", "deleteAccount": "Delete my account", "deleteConfirmTitle": "Confirm Deletion", "deleteConfirmMessage": "Are you sure you want to delete your account? All your data, including your progress and journal notes, will be permanently erased. This action is irreversible.", "accountDeletedSuccess": "Your account and all your data have been deleted.", "stats": {"sessionsCompleted": "Sessions completed", "daysStreak": "Consecutive days", "totalMinutes": "Total minutes"}}, "gender": {"masculine": "Ma<PERSON><PERSON><PERSON>", "feminine": "Feminine"}, "leaderboard": {"title": "Explorers Leaderboard", "description": "Discover your position among active members of the PiKnowKyo community. The ranking is based on engagement and progression (anonymized for privacy).", "noData": "The leaderboard is not yet available or is being calculated. Come back soon!", "pseudo": "<PERSON><PERSON><PERSON> (Anonymous)", "score": "Score", "you": "You", "privacyNote": "Your privacy is important. All pseudos are anonymized to protect your identity. Your participation in the leaderboard is optional and can be managed in your profile settings."}}