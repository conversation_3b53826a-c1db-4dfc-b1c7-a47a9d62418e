import React, { useState, useEffect, useContext } from 'react'; // <-- Ajout de useContext
import styled, { ThemeContext, DefaultTheme } from 'styled-components';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useLang, Language } from '../LangProvider';

// Hooks et Firebase
import { useAuth } from '../hooks/useAuth'; // Votre hook d'authentification
import { generateAnonymousPseudo } from '../hooks/useAuth'; // <-- Import de generateAnonymousPseudo
import { useAppDispatch, useAppSelector } from '../store/hooks'; // Hooks Redux
import { signOut, deleteUser, reauthenticateWithCredential, EmailAuthProvider } from "firebase/auth"; // Pour la déconnexion et suppression
// Pas besoin de doc, getDoc, updateDoc ici si tout passe par Redux slices
import { auth, db } from '../firebase'; // Firestore instance
// Si vous utilisez une Cloud Function pour la suppression, importez-la:
// import { getFunctions, httpsCallable } from 'firebase/functions';

// Slice et types du profil utilisateur
import {
  fetchUserProfile,
  updateUserProfile,
  UserProfileData, // Assurez-vous d'utiliser ce type pour userProfile
  GrammaticalGender,
  UserStats,
} from '../store/slices/userProfileSlice';

// Icônes
import { FiUser, FiLogOut, FiTrash2, FiGlobe, FiStar, FiSettings, FiShield, FiDollarSign, FiLoader, FiAlertCircle, FiRefreshCw } from 'react-icons/fi';


// --- Styled Components --- (Vos styled components existants)
const PageContainer = styled.div`
  padding: 1.5rem;
  max-width: 700px;
  margin: 0 auto;
  color: ${({ theme }) => theme.text};
`;

const PageHeader = styled.div`
  text-align: center;
  margin-bottom: 2.5rem;
  h1 {
    font-size: 2.4rem;
    color: ${({ theme }) => theme.primary};
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
  }
`;

const ProfileCard = styled.div`
  background: ${({ theme }) => theme.surface};
  border-radius: 16px;
  box-shadow: ${({ theme }) => theme.cardShadow};
  padding: 2rem;
  margin-bottom: 2rem;
  text-align: center;
`;

const AvatarPlaceholder = styled.div`
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.primary}33;
  color: ${({ theme }) => theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  margin: 0 auto 1rem auto;
  border: 3px solid ${({ theme }) => theme.primary};
`;

const AnonymousPseudoDisplay = styled.div`
  font-size: 1.2rem;
  font-weight: 600;
  color: ${({ theme }) => theme.text};
  margin-bottom: 0.25rem;
`;

const UserEmailDisplay = styled.div`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  margin-bottom: 1.5rem;
`;

const SettingsSection = styled.div`
  background: ${({ theme }) => theme.surfaceAlt};
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid ${({ theme }) => theme.border};
`;

const SectionTitle = styled.h2`
  font-size: 1.3rem;
  color: ${({ theme }) => theme.primary};
  margin-top: 0;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid ${({ theme }) => theme.border};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.2rem;
  label {
    display: block;
    font-weight: 500;
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
  }
  select, input[type="text"], input[type="password"] { /* <-- Ajout de input[type="password"] */
    width: 100%;
    padding: 0.7rem;
    border-radius: 8px;
    border: 1px solid ${({ theme }) => theme.border};
    background: ${({ theme }) => theme.inputBackground || theme.surface};
    color: ${({ theme }) => theme.text};
    font-size: 1rem;
    &:focus {
        outline: none;
        border-color: ${({ theme }) => theme.primary};
        box-shadow: 0 0 0 3px ${({ theme }) => theme.primary}40;
    }
  }
`;

const Button = styled.button<{ $variant?: 'primary' | 'danger' | 'secondary' }>`
  background: ${({ theme, $variant }) =>
    $variant === 'danger' ? (theme.errorColor || '#d9534f') :
    $variant === 'secondary' ? theme.secondary :
    theme.primary
  };
  color: ${({ theme }) => theme.textLight || '#fff'};
  border: none;
  border-radius: 8px;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 1rem;
  margin-right: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;

  &:hover { opacity: 0.85; }
  &:active { transform: scale(0.97); }
  &:disabled {
    background-color: ${({ theme }) => theme.disabledBackground || '#ccc'};
    color: ${({ theme }) => theme.disabledText || '#666'};
    cursor: not-allowed;
    opacity: 1;
    transform: none;
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const InfoText = styled.p`
  font-size: 0.9rem;
  color: ${({ theme }) => theme.textMuted};
  margin-top: 0.5rem;
  line-height: 1.5;
`;

const PseudoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
  padding: 0.8rem;
  background: ${({ theme }) => theme.surface};
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
`;

const PseudoText = styled.span`
  font-weight: 500;
  color: ${({ theme }) => theme.primary};
`;

const RefreshButton = styled.button`
  background: none;
  border: none;
  color: ${({ theme }) => theme.textSecondary};
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  transition: all 0.2s;

  &:hover {
    background: ${({ theme }) => theme.surfaceAlt}; /* Utilise surfaceAlt pour le hover */
    color: ${({ theme }) => theme.primary};
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin: 1.5rem 0;
`;

const StatCard = styled.div`
  background: ${({ theme }) => theme.surface};
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid ${({ theme }) => theme.border};
  text-align: center;

  .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: ${({ theme }) => theme.primary};
    display: block;
  }

  .stat-label {
    font-size: 0.85rem;
    color: ${({ theme }) => theme.textSecondary};
    margin-top: 0.25rem;
  }
`;

const ModalContent = styled.div`
  background: ${({ theme }) => theme.surface};
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.2);
  max-width: 450px;
  width: 90%;
  text-align: center;
  h3 {
    color: ${({ theme }) => theme.primary};
    margin-top: 0;
    margin-bottom: 1rem;
  }
  p {
    color: ${({ theme }) => theme.textSecondary};
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
  .actions {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.2rem;
  color: ${({ theme }) => theme.textSecondary};
  gap: 10px;

  svg {
    animation: spin 1.5s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
`;

const ErrorMessage = styled.p`
  color: ${({ theme }) => theme.errorColor};
  background-color: ${({ theme }) => theme.errorColor}1A; /* 10% opacity */
  border: 1px solid ${({ theme }) => theme.errorColor};
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem auto;
  max-width: 500px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
`;

const ProfilePage: React.FC = () => {
  const { t } = useTranslation();
  const { lang, setLang } = useLang();
  const theme = useContext(ThemeContext) as DefaultTheme;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const { user, loading: authLoading } = useAuth();

  // Sélectionner le profil utilisateur depuis le store Redux
  const { profile: userProfile, loading: profileLoading, saving: profileSaving, error: profileError } = useAppSelector(state => state.userProfile);

  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [reauthPassword, setReauthPassword] = useState('');
  const [localError, setLocalError] = useState<string | null>(null); // <-- Déclaration de l'état local d'erreur


  // Charger le profil utilisateur une fois que l'utilisateur est disponible
  useEffect(() => {
    if (user && !userProfile && !profileLoading && !profileError) {
      dispatch(fetchUserProfile(user.uid));
    }
  }, [user, userProfile, profileLoading, profileError, dispatch]);

  // Si l'utilisateur n'est pas connecté, rediriger ou afficher un message
  if (!user && !authLoading) {
    return (
        <PageContainer>
            <PageHeader><h1><FiUser /> {t('profile.notConnectedTitle')}</h1></PageHeader>
            <InfoText style={{textAlign: 'center'}}>
                {t('profile.pleaseLogin')}
            </InfoText>
            {/* Vous pourriez ajouter un bouton pour naviguer vers la page de login si votre AuthPage n'est pas la route par défaut */}
            {/* <Button onClick={() => navigate('/login')}>{t('auth.login.button')}</Button> */}
        </PageContainer>
    );
  }

  // Afficher le chargement du profil
  if (profileLoading || authLoading || !userProfile) { // Attendre que le profil soit chargé
    return <LoadingContainer><FiLoader /> {t('loading.profile')}</LoadingContainer>;
  }

  // Si une erreur de chargement du profil existe
  if (profileError) {
    return <ErrorMessage><FiAlertCircle /> {t('errors.cantLoadProfile')}: {profileError}</ErrorMessage>;
  }

  // Handle changes to preferences
  const handleGrammaticalGenderChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newGender = e.target.value as GrammaticalGender;
    if (userProfile && user) {
      dispatch(updateUserProfile({
        userId: user.uid,
        data: { preferences: { ...userProfile.preferences, preferedGender: newGender } }
      }));
    }
  };

  const handleRegeneratePseudo = async () => {
    if (!userProfile || !user) return;
    setLocalError(null); // Clear any previous local error

    try {
        const newPseudo = await generateAnonymousPseudo(user.uid + Date.now(), t, db); // <-- Appel de la fonction importée
        dispatch(updateUserProfile({
            userId: user.uid,
            data: { publicName: newPseudo }
        }));
    } catch (error: any) {
        console.error("Error regenerating pseudo:", error);
        setLocalError(t('errors.pseudoGenerationError', 'Impossible de générer un nouveau pseudo.'));
    }
  };

  const handleLogout = async () => {
    setLocalError(null); // Clear any previous local error
    try {
      await signOut(auth);
      console.log("User logged out successfully");
      navigate('/'); // Rediriger vers la page d'accueil ou de connexion
    } catch (error) {
      console.error("Error logging out:", error);
      setLocalError(t('errors.logoutError', 'Erreur lors de la déconnexion.')); // <-- Utilisation de setLocalError
    }
  };

  const handleDeleteAccount = async () => {
    if (!user) {
        setLocalError(t('errors.notAuthenticated', 'Utilisateur non authentifié.')); // <-- Utilisation de setLocalError
        return;
    }
    setLocalError(null); // Clear any previous local error

    try {
        // La réauthentification est nécessaire pour la suppression de compte
        // Si l'utilisateur est connecté via Google/Facebook, Firebase peut le gérer sans mot de passe
        // mais pour email/password, c'est crucial.
        if (user.providerData.some(provider => provider.providerId === 'password')) {
            if (!reauthPassword) {
                setLocalError(t('profile.deleteConfirmPasswordRequired', 'Veuillez entrer votre mot de passe pour confirmer la suppression.')); // <-- Utilisation de setLocalError
                return;
            }
            const credential = EmailAuthProvider.credential(user.email!, reauthPassword);
            await reauthenticateWithCredential(user, credential);
        }

        // Optionnel: Appeler une Cloud Function pour supprimer les données Firestore/Storage
        // const deleteUserDataCallable = httpsCallable(getFunctions(), 'deleteUserData');
        // await deleteUserDataCallable();

        // Supprimer l'utilisateur de Firebase Authentication
        await deleteUser(user);
        alert(t('profile.accountDeletedSuccess'));
        navigate('/'); // Rediriger après suppression
    } catch (error: any) {
        console.error("Error deleting account:", error);
        if (error.code === 'auth/requires-recent-login') {
            setLocalError(t('errors.reauthenticateRequired', 'Veuillez vous reconnecter pour effectuer cette action.')); // <-- Utilisation de setLocalError
        } else if (error.code === 'auth/wrong-password') {
            setLocalError(t('errors.wrongPassword', 'Mot de passe incorrect.')); // <-- Utilisation de setLocalError
        } else {
            setLocalError(t('errors.deleteAccountError', 'Une erreur est survenue lors de la suppression de votre compte.')); // <-- Utilisation de setLocalError
        }
    } finally {
        setShowDeleteConfirmModal(false);
        setReauthPassword(''); // Réinitialiser le champ
    }
  };


  const stats = userProfile.preferences.stats;

  return (
    <PageContainer>
      <PageHeader>
        <h1><FiUser /> {t('profile.title')}</h1>
      </PageHeader>

      <ProfileCard>
        <AvatarPlaceholder>
          {userProfile.publicName ? userProfile.publicName.charAt(0).toUpperCase() : <FiUser />}
        </AvatarPlaceholder>

        <PseudoContainer>
          <div style={{flex: 1}}>
            <div style={{fontSize: '0.85rem', color: theme.textSecondary, marginBottom: '0.25rem'}}>
              {t('profile.publicPseudo')}
            </div>
            <PseudoText>{userProfile.publicName}</PseudoText>
          </div>
          <RefreshButton
            onClick={handleRegeneratePseudo}
            disabled={profileSaving} // Désactiver pendant la sauvegarde
            title={t('profile.regeneratePseudo')}
          >
            <FiRefreshCw style={{animation: profileSaving ? 'spin 1s linear infinite' : 'none'}} />
          </RefreshButton>
        </PseudoContainer>

        {userProfile.email && <UserEmailDisplay>{userProfile.email}</UserEmailDisplay>}

        {/* Afficher le statut Premium si applicable */}
        {userProfile.preferences.subscriptions.active && ( // Accéder via preferences.subscriptions
            <p style={{color: theme.primary, fontWeight: 'bold', margin: '1rem 0'}}>
              <FiStar style={{marginRight: '0.3em'}} /> {t('profile.premiumMember')}
            </p>
        )}

        <StatsContainer>
          <StatCard>
            <span className="stat-value">{stats.totalSessionsPlayed}</span>
            <div className="stat-label">{t('profile.stats.sessionsCompleted')}</div>
          </StatCard>
          <StatCard>
            <span className="stat-value">{stats.currentStreak}</span>
            <div className="stat-label">{t('profile.stats.daysStreak')}</div>
          </StatCard>
          <StatCard>
            <span className="stat-value">{stats.totalMinutesPlayed}</span>
            <div className="stat-label">{t('profile.stats.totalMinutes')}</div>
          </StatCard>
        </StatsContainer>

         <Button $variant="secondary" onClick={() => navigate('/monetization')} style={{fontSize: '0.9rem', padding: '0.6rem 1rem', width: '100%'}} disabled={profileSaving}>
            <FiDollarSign /> {userProfile.preferences.subscriptions.active ? t('profile.manageSubscription') : t('profile.upgradeToPremium')}
        </Button>
      </ProfileCard>

      <SettingsSection>
        <SectionTitle><FiSettings /> {t('profile.preferencesTitle')}</SectionTitle>
        <FormGroup>
          <label htmlFor="app-language">{t('profile.appLanguage')}</label>
          <select id="app-language" value={lang} onChange={e => {
            setLang(e.target.value as Language);
            if(user) { // Sauvegarder dans Firestore aussi
                dispatch(updateUserProfile({
                    userId: user.uid,
                    data: { preferences: { ...userProfile.preferences, language: e.target.value } }
                }));
            }
          }} disabled={profileSaving}>
            <option value="fr">{t('languages.french')}</option>
            <option value="en">{t('languages.english')}</option>
            <option value="es">{t('languages.spanish')}</option>
          </select>
        </FormGroup>
        <FormGroup>
          <label htmlFor="grammatical-gender">{t('profile.grammaticalGenderLabel')}</label>
          <select
            id="grammatical-gender"
            value={userProfile.preferences.preferedGender || 'masculine'}
            onChange={handleGrammaticalGenderChange}
            disabled={profileSaving}
          >
            <option value="masculine">{t('gender.masculine')}</option>
            <option value="feminine">{t('gender.feminine')}</option>
            <option value="neutral">{t('gender.neutral')}</option>
          </select>
          <InfoText>{t('profile.grammaticalGenderInfo')}</InfoText>
        </FormGroup>

        <div style={{marginTop: '1.5rem', padding: '1rem', background: theme.surface, borderRadius: '8px', border: `1px solid ${theme.border}`}}>
          <h4 style={{margin: '0 0 0.5rem 0', color: theme.primary}}>{t('profile.quickActions')}</h4>
          <div style={{display: 'flex', gap: '0.5rem', flexWrap: 'wrap'}}>
            <Button $variant="secondary" onClick={() => navigate('/settings')} style={{fontSize: '0.85rem', padding: '0.5rem 1rem'}} disabled={profileSaving}>
              <FiSettings /> {t('profile.goToSettings')}
            </Button>
            <Button $variant="secondary" onClick={() => navigate('/stats')} style={{fontSize: '0.85rem', padding: '0.5rem 1rem'}} disabled={profileSaving}>
              <FiStar /> {t('profile.viewStats')}
            </Button>
          </div>
        </div>

      </SettingsSection>

      <SettingsSection>
        <SectionTitle><FiShield /> {t('profile.accountActionsTitle')}</SectionTitle>
        <Button onClick={handleLogout} $variant="secondary" disabled={profileSaving}>
          <FiLogOut /> {t('profile.logout')}
        </Button>
        <Button onClick={() => setShowDeleteConfirmModal(true)} $variant="danger" disabled={profileSaving}>
          <FiTrash2 /> {t('profile.deleteAccount')}
        </Button>
      </SettingsSection>

      {showDeleteConfirmModal && (
        <ModalOverlay onClick={() => setShowDeleteConfirmModal(false)}>
          <ModalContent onClick={e => e.stopPropagation()}>
            <h3><FiAlertCircle style={{marginRight: '0.5rem', color: theme.errorColor || 'red'}} />{t('profile.deleteConfirmTitle')}</h3>
            <p>{t('profile.deleteConfirmMessage')}</p>
            {user?.providerData.some(provider => provider.providerId === 'password') && (
                <FormGroup>
                    <label htmlFor="reauth-password">{t('profile.confirmPasswordLabel')}</label>
                    <input
                        id="reauth-password"
                        type="password"
                        value={reauthPassword}
                        onChange={(e) => setReauthPassword(e.target.value)}
                        placeholder={t('profile.confirmPasswordPlaceholder')}
                        disabled={profileSaving}
                    />
                </FormGroup>
            )}
            {localError && <ErrorMessage>{localError}</ErrorMessage>} {/* <-- Affichage de l'erreur locale */}
            <div className="actions">
              <Button onClick={() => { setShowDeleteConfirmModal(false); setLocalError(null); setReauthPassword(''); }} $variant="secondary" disabled={profileSaving}>
                {t('actions.cancel')}
              </Button>
              <Button onClick={handleDeleteAccount} $variant="danger" disabled={profileSaving}>
                {profileSaving ? <FiLoader style={{animation: 'spin 1s linear infinite'}}/> : <FiTrash2 />}
                {profileSaving ? t('actions.deleting') : t('actions.deleteConfirm')}
              </Button>
            </div>
          </ModalContent>
        </ModalOverlay>
      )}
    </PageContainer>
  );
};

export default ProfilePage;