// src/store/slices/journalSlice.ts
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  collection,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  query,
  orderBy,
  Timestamp, // Importez Timestamp
} from 'firebase/firestore';
import { db } from '../../firebase';

// Si l'interface JournalEntry n'est pas précisément définie dans '../../models',
// ou si elle est vide, voici une définition commune et compatible avec Firestore :
export interface JournalEntry {
  id: string; // ID Firestore du document
  userId: string; // UID de l'utilisateur propriétaire
  title: string;
  content: string;
  createdAt: number; // Unix timestamp en millisecondes
  mood?: string; // Exemple de champ
  // Ajoutez d'autres champs que votre journal pourrait avoir
}

export interface JournalState {
  entries: JournalEntry[];
  loading: boolean;
  error: string | null;
  lastSyncTimestamp: number | null;
  pendingChanges: { // Pourrait être utilisé pour une synchronisation offline ou optimistic UI
    created: JournalEntry[];
    updated: JournalEntry[];
    deleted: string[];
  };
}

const initialState: JournalState = {
  entries: [],
  loading: false,
  error: null,
  lastSyncTimestamp: null,
  pendingChanges: {
    created: [],
    updated: [],
    deleted: [],
  },
};

// Thunk pour récupérer les entrées de journal d'un utilisateur spécifique
export const fetchJournalEntries = createAsyncThunk(
  'journal/fetchJournalEntries',
  async (userId: string, { rejectWithValue }) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to fetch journal entries.");
      }
      const journalCollectionRef = collection(db, 'users', userId, 'journalEntries');
      const q = query(journalCollectionRef, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      const entries = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          // Convertir Timestamp en number (millisecondes Unix)
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().getTime() : data.createdAt,
        } as JournalEntry;
      });
      return entries;
    } catch (error: any) {
      console.error("Error fetching journal entries:", error);
      return rejectWithValue(error.message || "Failed to fetch journal entries.");
    }
  }
);

// Thunk pour ajouter une nouvelle entrée de journal
export const addJournalEntry = createAsyncThunk(
  'journal/addJournalEntry',
  async (
    { userId, entry }: { userId: string, entry: Omit<JournalEntry, 'id' | 'createdAt' | 'userId'> },
    { rejectWithValue }
  ) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to add journal entry.");
      }
      const journalCollectionRef = collection(db, 'users', userId, 'journalEntries');
      const newEntryData = { ...entry, userId, createdAt: Date.now() }; // Ajout userId et timestamp
      const docRef = await addDoc(journalCollectionRef, newEntryData);

      return { id: docRef.id, ...newEntryData } as JournalEntry;
    } catch (error: any) {
      console.error("Error adding journal entry:", error);
      return rejectWithValue(error.message || "Failed to add journal entry.");
    }
  }
);

// Thunk pour mettre à jour une entrée de journal existante
export const updateJournalEntry = createAsyncThunk(
  'journal/updateJournalEntry',
  async ({ userId, entry }: { userId: string, entry: JournalEntry }, { rejectWithValue }) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to update journal entry.");
      }
      const docRef = doc(db, 'users', userId, 'journalEntries', entry.id);

      // --- CORRECTION CLÉ ICI ---
      // Extrayez l'ID et l'userId car ils font partie du chemin du document et
      // ne doivent pas être mis à jour comme des champs dans le document lui-même.
      // Le reste des propriétés (`dataToUpdate`) est ce qui doit être mis à jour.
      const { id, userId: entryUserId, ...dataToUpdate } = entry;
      await updateDoc(docRef, dataToUpdate); // Passez l'objet de mise à jour partielle

      return entry; // Retournez l'entrée complète pour mettre à jour le store Redux
    } catch (error: any) {
      console.error("Error updating journal entry:", error);
      return rejectWithValue(error.message || "Failed to update journal entry.");
    }
  }
);

// Thunk pour supprimer une entrée de journal
export const deleteJournalEntry = createAsyncThunk(
  'journal/deleteJournalEntry',
  async ({ userId, entryId }: { userId: string, entryId: string }, { rejectWithValue }) => {
    try {
      if (!userId) {
        throw new Error("User ID is required to delete journal entry.");
      }
      const docRef = doc(db, 'users', userId, 'journalEntries', entryId);
      await deleteDoc(docRef);
      return entryId; // Retourne l'ID de l'entrée supprimée pour le reducer
    } catch (error: any) {
      console.error("Error deleting journal entry:", error);
      return rejectWithValue(error.message || "Failed to delete journal entry.");
    }
  }
);


const journalSlice = createSlice({
  name: 'journal',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateLastSyncTimestamp: (state, action: PayloadAction<number>) => {
      state.lastSyncTimestamp = action.payload;
    },
    clearPendingChanges: (state) => {
      state.pendingChanges = {
        created: [],
        updated: [],
        deleted: [],
      };
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchJournalEntries
      .addCase(fetchJournalEntries.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchJournalEntries.fulfilled, (state, action: PayloadAction<JournalEntry[]>) => {
        state.loading = false;
        state.entries = action.payload;
        state.lastSyncTimestamp = Date.now();
      })
      .addCase(fetchJournalEntries.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // addJournalEntry
      .addCase(addJournalEntry.fulfilled, (state, action: PayloadAction<JournalEntry>) => {
        state.entries.unshift(action.payload); // Ajoute la nouvelle entrée au début
      })
      // updateJournalEntry
      .addCase(updateJournalEntry.fulfilled, (state, action: PayloadAction<JournalEntry>) => {
        const index = state.entries.findIndex(entry => entry.id === action.payload.id);
        if (index !== -1) {
          state.entries[index] = action.payload; // Met à jour l'entrée existante
        }
      })
      // deleteJournalEntry
      .addCase(deleteJournalEntry.fulfilled, (state, action: PayloadAction<string>) => {
        state.entries = state.entries.filter(entry => entry.id !== action.payload); // Supprime l'entrée
      });
  },
});

export const {
  clearError,
  updateLastSyncTimestamp,
  clearPendingChanges,
} = journalSlice.actions;

export default journalSlice.reducer;