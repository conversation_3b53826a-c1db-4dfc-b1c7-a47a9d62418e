// src/hooks/useContentAccess.ts
import { useState, useEffect } from 'react';
import { useAppSelector } from '../store/hooks';
import { AdsService } from '../services/adsService';

export interface ContentAccessState {
  canAccess: boolean;
  reason: 'free' | 'premium' | 'ad_free_period' | 'blocked';
  requiresAd: boolean;
  canWatchAd: boolean;
  adsWatchedToday: number;
  maxAdsPerDay: number;
  timeRemaining: number;
  isLoading: boolean;
}

export const useContentAccess = (contentType: string) => {
  const { profile: userProfile, loading: profileLoading } = useAppSelector(state => state.userProfile);
  const [accessState, setAccessState] = useState<ContentAccessState>({
    canAccess: false,
    reason: 'blocked',
    requiresAd: false,
    canWatchAd: false,
    adsWatchedToday: 0,
    maxAdsPerDay: 3,
    timeRemaining: 0,
    isLoading: true,
  });

  useEffect(() => {
    if (profileLoading) {
      setAccessState(prev => ({ ...prev, isLoading: true }));
      return;
    }

    const accessResult = AdsService.canAccessContent(contentType, userProfile);
    let canWatchResult = { canWatch: false, reason: '', adsWatchedToday: 0, maxAds: 3 };
    let timeRemaining = 0;

    if (userProfile) {
      canWatchResult = AdsService.canWatchAd(userProfile);
      timeRemaining = AdsService.getAdFreeTimeRemaining(userProfile);
    }

    setAccessState({
      canAccess: accessResult.canAccess,
      reason: accessResult.reason,
      requiresAd: accessResult.requiresAd,
      canWatchAd: canWatchResult.canWatch,
      adsWatchedToday: canWatchResult.adsWatchedToday,
      maxAdsPerDay: canWatchResult.maxAds,
      timeRemaining,
      isLoading: false,
    });
  }, [contentType, userProfile, profileLoading]);

  return accessState;
};

export default useContentAccess;
