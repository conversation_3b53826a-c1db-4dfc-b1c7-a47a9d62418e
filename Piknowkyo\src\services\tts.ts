// src/services/tts.ts (VERSION COMPLÈTE ET CORRIGÉE, AVEC VOS FOURNISSEURS)

export type TTSProvider =
  | 'browser'
  | 'piper'
  | 'ttswebui'
  | 'openvoice'
  | 'parlertts'
  | 'murf'
  | 'apicustom'
  | 'cloud';

export interface TTSConfig {
  volume?: number;        // 0.0 to 1.0
  rate?: number;          // 0.1 to 10
  pitch?: number;         // 0 to 2
  signal?: AbortSignal;   // Pour annuler la lecture
  apiUrl?: string;        // Pour 'apicustom'
  apiKey?: string;        // Pour 'apicustom' ou 'cloud'
}

// --- Fonctions spécifiques aux providers (placeholders pour la plupart, ajustées) ---

export async function ttsPiper(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Piper (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici (appel WASM, API locale, etc.)
  return new Promise(resolve => setTimeout(resolve, text.length * 50)); // Simule la lecture
}

export async function ttsWebUI(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS WebUI (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici (appel à votre TTS-WebUI)
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}

export async function ttsOpenVoice(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS OpenVoice (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}
export async function ttsMurf(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Murf (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}
export async function ttsParlerTTS(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS ParlerTTS (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}
export async function ttsApiCustom(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS ApiCustom (Simulé): "${text}", URL: ${config.apiUrl}, Voice: ${voice}, Config:`, config);
  // Implémentation réelle ici (appel à votre API personnalisée)
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}
export async function ttsCloud(text: string, voice: string, lang: string, config: TTSConfig): Promise<void> {
  console.log(`TTS Cloud (Simulé): "${text}", Voice: ${voice}, Lang: ${lang}, Config:`, config);
  // Implémentation réelle ici (appel à un service cloud, ex: Google Cloud TTS via un backend)
  return new Promise(resolve => setTimeout(resolve, text.length * 50));
}


// --- Synthèse vocale du navigateur (Web Speech API) ---
let currentUtterance: SpeechSynthesisUtterance | null = null;

export async function ttsBrowser(
  text: string,
  voiceName: string, // Nom de la voix ou 'auto'
  lang: string,
  config: TTSConfig
): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!window.speechSynthesis) {
      reject(new Error("Web Speech API (SpeechSynthesis) n'est pas supportée par ce navigateur."));
      return;
    }

    if (config.signal?.aborted) {
      reject(new DOMException('Lecture annulée', 'AbortError'));
      return;
    }

    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
    }

    const utter = new SpeechSynthesisUtterance(text);
    currentUtterance = utter; // Garder une référence pour le contrôle global

    utter.lang = lang;
    utter.volume = config.volume ?? 1;
    utter.rate = config.rate ?? 1;
    utter.pitch = config.pitch ?? 1;

    // --- Logique de sélection de la voix plus robuste ---
    const selectAndSetVoice = () => {
      const voices = window.speechSynthesis.getVoices();
      let selectedVoice = null;
      if (voiceName && voiceName !== 'auto') {
        selectedVoice = voices.find(v => v.name === voiceName && v.lang.startsWith(lang));
      }
      if (!selectedVoice) {
        selectedVoice = voices.find(v => v.lang.startsWith(lang)); // Fallback par langue
      }
      if (selectedVoice) {
        utter.voice = selectedVoice;
      } else {
        console.warn(`Aucune voix trouvée pour "${voiceName}" ou "${lang}". Utilisation de la voix par défaut du navigateur.`);
      }
    };

    if (window.speechSynthesis.getVoices().length === 0) {
      // Les voix peuvent ne pas être chargées au premier appel. Attendre l'événement.
      window.speechSynthesis.onvoiceschanged = () => {
        selectAndSetVoice();
        window.speechSynthesis.onvoiceschanged = null; // Nettoyer l'écouteur
      };
    } else {
      selectAndSetVoice();
    }

    const onEnd = () => {
      if (currentUtterance === utter) currentUtterance = null;
      resolve();
    };

    const onError = (event: SpeechSynthesisErrorEvent) => {
      if (currentUtterance === utter) currentUtterance = null;
      if (event.error !== 'canceled' && event.error !== 'interrupted') {
        reject(new Error(`Erreur de synthèse vocale: ${event.error}`));
      } else {
        // En cas d'annulation, la promesse doit être résolue ou rejetée pour ne pas bloquer.
        // Puisque SpeechSynthesis.cancel() peut déclencher 'error', on résout ici.
        resolve();
      }
    };

    utter.onend = onEnd;
    utter.onerror = onError;

    const signalListener = () => {
      window.speechSynthesis.cancel();
      if (currentUtterance === utter) currentUtterance = null;
    };

    if (config.signal) {
      if (config.signal.aborted) {
        signalListener();
        return;
      }
      config.signal.addEventListener('abort', signalListener, { once: true });
      // Assurez-vous que les listeners d'événements de l'utterance sont bien gérés avec le signal
      // C'est complexe avec Web Speech API. Souvent, cancel() suffit à déclencher onerror.
      utter.onend = () => { config.signal?.removeEventListener('abort', signalListener); onEnd(); };
      utter.onerror = (event) => { config.signal?.removeEventListener('abort', signalListener); onError(event); };
    }

    window.speechSynthesis.speak(utter);
  });
}


// --- Fonction principale pour jouer le TTS ---
export async function ttsPlay(
  provider: TTSProvider,
  text: string,
  voice: string,
  lang: string,
  config: TTSConfig = {}
): Promise<void> {
  switch (provider) {
    case 'piper':
      return ttsPiper(text, voice, lang, config);
    case 'ttswebui':
      return ttsWebUI(text, voice, lang, config);
    case 'openvoice':
      return ttsOpenVoice(text, voice, lang, config);
    case 'parlertts':
      return ttsParlerTTS(text, voice, lang, config);
    case 'murf':
      return ttsMurf(text, voice, lang, config);
    case 'apicustom':
      return ttsApiCustom(text, voice, lang, config);
    case 'cloud':
      return ttsCloud(text, voice, lang, config);
    case 'browser': // Fallback si le provider est 'browser'
    default: // Ou si le provider est inconnu, par défaut sur 'browser'
      return ttsBrowser(text, voice, lang, config);
  }
}

// --- Fonction pour arrêter la lecture TTS (principalement pour le navigateur) ---
export function ttsStop(abortController?: AbortController | null): void {
  if (window.speechSynthesis && window.speechSynthesis.speaking) {
    window.speechSynthesis.cancel();
  }
  if (currentUtterance) {
    currentUtterance = null;
  }
  if (abortController && !abortController.signal.aborted) {
    abortController.abort();
  }
}