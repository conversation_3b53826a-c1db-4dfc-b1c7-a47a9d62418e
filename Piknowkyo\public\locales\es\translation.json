{"navigation": {"home": "<PERSON><PERSON>o", "sessions": "Sesiones", "games": "<PERSON><PERSON><PERSON>", "journal": "Diario", "stats": "Estadísticas", "leaderboard": "Clasificación", "blog": "Blog", "profile": "Perfil", "monetization": "Premium", "settings": "Configuración", "about": "Acerca de"}, "common": {"welcome": "Bienvenido a PiKnowKyo", "ok": "OK", "cancel": "<PERSON><PERSON><PERSON>", "save": "Guardar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Atrás", "next": "Siguient<PERSON>", "previous": "Anterior", "loading": "Cargando...", "error": "Error", "success": "Éxito"}, "home": {"title": "Bienvenido a PiKnowKyo", "subtitle": "Tu compañero personal de bienestar. Descubre sesiones guiadas, sigue tu progreso y cultiva tu crecimiento interior.", "exploreButton": "Explorar Sesiones", "welcomeText": "Comienza tu viaje hacia un bienestar sostenible con nuestras herramientas personalizadas y nuestra comunidad solidaria.", "quickAccess": "Acceso Rápido", "learnMore": "Aprende más sobre PiKnowKyo"}, "sessions": {"title": "Sesiones de Bienestar", "description": "Descubre nuestra colección de sesiones guiadas para tu desarrollo personal.", "searchPlaceholder": "Buscar una sesión...", "allTypes": "Todos los tipos", "allDurations": "Todas las duraciones", "durationLabel": "Duración", "type": "Tipo", "category": "Categoría", "difficulty": "Dificultad", "noSessionsFound": "No se encontraron sesiones que coincidan con tus criterios.", "clearFilters": "Limpiar filtros", "filters": "<PERSON><PERSON><PERSON>", "filterBy": "Filtrar por", "sessionType": "Tipo de sesión", "gridView": "Vista de Cuadrícula", "listView": "Vista de Lista", "noResultsMatchCriteria": "Ninguna sesión coincide con tus criterios.", "noSessionsAvailable": "No hay sesiones disponibles en este momento.", "meditation": "Meditación", "hypnosis": "Hipnosis", "affirmations": "Afirmaciones", "custom": "Personalizado", "viewModes": {"grid": "Cuadrícula", "list": "Lista"}, "duration": {"label": "Duración", "under15": "Menos de 15 min", "15to30": "15 - 30 min", "over30": "Más de 30 min"}}, "games": {"title": "Mini-Juegos de Desarrollo Personal", "intro": "Prueba y mejora tus habilidades con nuestros mini-juegos divertidos y desafiantes.", "estimatedDuration": "Duración estimada", "personalBest": "Récord personal", "continueGame": "Continuar partida", "newGame": "Nueva partida", "gameInfo": "Información del juego", "gameRules": "Reglas del juego", "gameOver": "<PERSON><PERSON> termina<PERSON>", "finalScore": "Puntuación final", "newRecord": "¡Nuevo récord!", "playAgain": "<PERSON><PERSON> de nuevo", "backToGames": "Volver a los juegos", "pause": "Pausa", "resume": "<PERSON><PERSON><PERSON>", "quit": "Salir", "maxLevels": "Este juego contiene {{maxLevels}} niveles de dificultad.", "yourBestScore": "Tu mejor puntuación en este juego es de {{score}} puntos.", "keywords": "Palabras clave", "gameOverSummary": "¡Felicidades! Tu puntuación final es de {{score}} puntos y alcanzaste el nivel {{level}} en {{time}} segundos.", "zenTetris": {"title": "Zen Tetris", "description": "Una versión relajante del famoso juego de bloques. Mejora tu concentración y gestión del estrés.", "rules": "Coloca las piezas que caen para completar líneas horizontales. Cuantas más líneas elimines de una vez, más puntos ganas. El juego se acelera gradualmente.", "controls": "Controles táctiles", "tips": "Man<PERSON>n la calma, planifica tus movimientos y trata de crear combos para maximizar tu puntuación.", "touchTap": "Toque rápido: Rotación", "touchLeft": "<PERSON><PERSON><PERSON>: Mover a la izquierda", "touchRight": "<PERSON><PERSON><PERSON> derecha: Mover a la derecha", "touchDown": "<PERSON><PERSON><PERSON> abajo: Caída suave", "touchButtons": "Botones de control en la parte inferior"}}, "game": {"pauseButton": "Pausar", "info": "Info", "level": "<PERSON><PERSON>", "lines": "Líneas", "score": "Puntuación", "time": "Tiempo", "nextPiece": "Siguiente Pieza", "moveLeft": "Mover a la izquierda", "moveRight": "Mover a la derecha", "rotate": "R<PERSON><PERSON>", "softDrop": "Caída suave", "modal": {"rulesTitle": "Reglas del Juego", "pausedTitle": "Juego en Pausa", "gameOverTitle": "¡Juego Terminado!", "pausedMessage": "Tu juego está en pausa. Reanúdalo cuando estés listo.", "gameOverMessage": "¡Bien jugado! Tu puntuación final es {{score}} y alcanzaste el nivel {{level}}.", "return": "Volver", "restart": "Reiniciar", "resume": "<PERSON><PERSON><PERSON>", "start": "Comenzar"}, "controls": {"keyboard": "Teclado", "touch": "Táctil"}, "zenTetris": {"rules1": "Apila bloques para formar líneas completas y anota puntos. ¡La velocidad aumenta con los niveles!", "rules2": "Controles:", "ruleMoveLeft": "Mover a la izquierda", "ruleMoveRight": "Mover a la derecha", "ruleSoftDrop": "Caída suave", "ruleRotate": "R<PERSON><PERSON>", "rulePause": "Pausa"}}, "journal": {"title": "Diario de Seguimiento", "description": "Encuentra aquí todas tus notas personales, organizadas por sesión. Reflexiona sobre tus experiencias y sigue tu progreso.", "noNotesYet": "Tu diario aún está vacío.", "startSessionPrompt": "Comienza una sesión y toma notas para ver tus reflexiones aquí.", "unknownSession": "Sesión (ID: {{id}})", "notesPlural": "notas", "noteSingular": "nota", "seeAllNotes": "Ver todas las {{count}} notas...", "trackingJournal": "Diario de Seguimiento", "addEntry": "Añade tu reflexión sobre esta sesión..."}, "stats": {"title": "Tus Estadísticas de Bienestar", "description": "Sigue tu viaje, celebra tu progreso y descubre tus tendencias.", "sessionsFollowed": "Sesiones Practicadas", "sessionsFollowedDesc": "Número de sesiones únicas con notas.", "totalTime": "Tiempo Total en Sesión", "totalTimeDesc": "Tiempo acumulado estimado.", "favoriteSession": "Sesión Favorita", "favoriteSessionDesc": "La mejor valorada.", "notesWritten": "Total de Notas Escritas", "notesWrittenDesc": "Número de reflexiones registradas.", "typesFollowed": "Distribución por Tipo de Sesión", "timePerSession": "Detalle por Sesión (Estimado)", "noTypesYet": "Aún no se ha seguido ningún tipo de sesión específico.", "noTimePerSession": "No hay datos de tiempo por sesión disponibles.", "timesPlural": "veces", "timesSingular": "vez", "notesPlural": "notas", "noteSingular": "nota", "duration": {"min": "min", "h": "h"}}, "blog": {"title": "Diario Comunitario", "description": "Comparte tus experiencias, descubrimientos e inspiraciones con la comunidad PiKnowKyo. Todos los mensajes son anónimos.", "searchPlaceholder": "Buscar mensajes...", "allCategories": "Todas las categorías", "writeNewPost": "Escribir un nuevo mensaje", "postPlaceholder": "<PERSON> mensaje (será publicado de forma anónima)...", "category": "Categoría", "publishing": "Publicando...", "publish": "Publicar", "loginToPost": "Debes estar conectado para publicar un mensaje.", "noPostsYet": "Aún no hay mensajes en esta categoría o que coincidan con tu búsqueda.", "like": "Me gusta", "comments": "Comentarios", "addComment": "Añadir un comentario", "commentPlaceholder": "<PERSON> comentario (anónimo)...", "postComment": "Publicar comentario", "noCommentsYet": "Aún no hay comentarios. ¡Sé el primero en comentar!", "backToBlog": "Volver al blog", "postNotFound": "Mensaje no encontrado", "commentsSectionTitle": "Comentarios", "yourCommentPlaceholder": "Tu comentario...", "sending": "Enviando...", "sendComment": "Enviar", "loginToComment": "Inicia sesión para añadir un comentario.", "sampleAuthor": "Autor <PERSON>", "samplePostContent": "Contenido detallado del mensaje. Este mensaje habla sobre la importancia de la atención plena en nuestras vidas diarias estresantes y cómo ejercicios simples pueden aportar gran paz interior.", "sampleCommenter1": "Comentarista1", "sampleCommenter2": "OtraPersona", "sampleComment1": "¡Gran mensaje!", "sampleComment2": "<PERSON><PERSON>, gracias por compartir.", "anonymousUser": "<PERSON><PERSON><PERSON>", "categories": {"général": "General", "gratitude": "<PERSON><PERSON><PERSON><PERSON>", "défis": "Desafíos", "inspirations": "Inspiraciones", "questions": "Preguntas"}}, "about": {"title": "Acerca de", "description": "Descubre PiKnowKyo, tu compañero de crecimiento personal y bienestar.", "philosophy": {"title": "Nuestra Filosofía: El Viaje de Pi a Kyo", "pi": {"title": "Pi (π)", "description": "El infinito, el misterio sagrado del universo y la armonía fundamental que nos une a todos. Es el punto de partida, la apertura a lo desconocido."}, "know": {"title": "Know (<PERSON><PERSON><PERSON>)", "description": "La exploración, el aprendizaje estructurado y la claridad mental. Es la adquisición de herramientas y comprensiones para navegar el camino."}, "kyo": {"title": "<PERSON><PERSON> (教え)", "description": "La enseñanza, la sabiduría encarnada, la iluminación y el compartir altruista de la luz descubierta. Es la culminación y la irradiación."}, "conclusion": "PiKnowKyo es más que una aplicación, es una brújula para tu crecimiento interior, inspirada por"}, "tools": {"title": "Nuestras Herramientas para tu Crecimiento", "description": "Ofrecemos una gama variada de sesiones y herramientas diseñadas para acompañarte en tu camino de crecimiento personal:", "hypnosis": "Hipnosis Evolutiva para explorar tu subconsciente e iniciar cambios profundos.", "meditation": "Meditaciones Guiadas para cultivar la atención plena, la paz interior y la resistencia emocional.", "affirmations": "Afirmaciones Positivas para reprogramar tus pensamientos y fortalecer tu confianza en ti mismo.", "nlp": "Entrenamiento PNL (Programación Neuro-Lingüística) para mejorar tu comunicación y alcanzar tus objetivos.", "stories": "Historias Metafóricas para estimular tu imaginación y facilitar la integración de nuevas perspectivas."}, "experience": {"title": "Una Experiencia Holística Diseñada para Ti", "audio": "Audio 100% configurable (música, ambiente, voz, binaural).", "guidedPaths": "Caminos guiados y creación de sesiones personalizadas.", "community": "Comunidad solidaria y clasificación anónima (opcional).", "blog": "Blog interno con artículos, consejos y recursos inspiradores.", "multilang": "Soporte multi-idioma y temas claro/oscuro personalizables.", "notifications": "Notificaciones de motivación suaves para acompañarte."}, "features": {"customizable": "Sesiones completamente personalizables según tus necesidades.", "journal": "Diario personal para seguir tu progreso y reflexiones.", "stats": "Estadísticas detalladas para visualizar tu evolución."}, "monetization": {"title": "Monetización Ética:", "description": "Ofrecemos una prueba gratuita, suscripción opcional para acceso completo, anuncios mínimos y no intrusivos (evitables con suscripción), y la posibilidad de donaciones para apoyar nuestra misión."}, "community": {"title": "Únete a Nuestra Comunidad", "description": "PiKnowKyo está diseñado para aquellos que valoran el autoconocimiento, la organización de sus pensamientos y su productividad personal con un enfoque en el bienestar. Ya seas estudiante, profesional, investigador, o simplemente una mente curiosa en busca de armonía, nuestra aplicación es tu aliada.", "contact": "Para cualquier pregunta, sugerencia o si necesitas asistencia, no dudes en enviarnos un correo electrónico a:", "website": "También puedes visitar nuestro sitio web", "moreInfo": "para más información"}}, "settings": {"title": "Configuración", "audio": "Audio", "language": "Idioma", "voice": "Voz", "autoVoice": "Voz automática", "testVoice": "Probar voz", "saveConfig": "Guardar configuración", "ttsSectionTitle": "Sín<PERSON>is de voz (TTS)", "ttsProvider": "Proveedor TTS", "ttsTestText": "Esta es una prueba de síntesis de voz.", "ttsTestError": "Error al probar la voz", "downloadingVoice": "Descargando voz...", "voiceDownloaded": "Voz <PERSON>", "noVoiceForSelection": "No hay voz disponible para esta selección", "noSpecificVoiceForLang": "No hay voz específica para este idioma. Aquí están todas las voces disponibles:", "explanationsTitle": "Explicaciones", "audioAssetsManagementTitle": "Gestión de archivos de audio", "audioAssetsInfo": "Gestiona tu música personalizada y sonidos ambientales", "goToAudioAssets": "Gestionar archivos de audio", "providerLabels": {"browser": "<PERSON><PERSON><PERSON><PERSON>", "piper": "Piper (IA)"}, "ttsProviderInfo": {"browser": "Usa las voces integradas del navegador", "piper": "Voces IA de alta calidad (requiere descarga)"}, "modal": {"saveSuccessTitle": "Configuración guardada", "saveSuccessMessage": "Tu configuración se ha guardado exitosamente", "testErrorTitle": "<PERSON><PERSON><PERSON>"}}, "monetization": {"title": "Nuestros Planes y Apoyo", "description": "Elige el plan que te convenga o apoya nuestra misión para seguir ofreciendo herramientas de bienestar accesibles.", "currentPlan": "Tu Plan Actual: Premium", "subCancelsOn": "Tu suscripción expirará el", "subRenewsOn": "Próxima renovación el", "manageSub": "Gestionar mi suscripción", "donateTitle": "<PERSON><PERSON><PERSON>", "donateDescription": "Tu generosidad nos ayuda a mantener y mejorar la aplicación para todos. ¡Cada contribución cuenta!", "donateButton": "Hacer una donación", "donateAlert": "¡Gracias por tu interés! La funcionalidad de donación estará disponible pronto. Mientras tanto, puedes visitar nuestro sitio web.", "manageSubAlert": "Simulación: Redirigiendo al portal de gestión de suscripción. Podrías cancelar o actualizar tu suscripción aquí."}, "sessionDetails": {"description": "Descripción", "expectedBenefits": "<PERSON><PERSON><PERSON><PERSON>", "keywords": "Palabras clave", "startSession": "<PERSON><PERSON><PERSON>", "backToSessions": "Volver a las Sesiones", "audioConfigGlobal": "Configuración de Audio de la Sesión", "userReviews": "Reseñas de Usuarios", "yourNotes": "Tu Diario para esta Sesión", "previousNotes": "Notas anteriores", "dnd": {"label": "No Molestar (Aplicación)", "permissionNeededInfo": "Activar solicitará permiso de notificación para optimizar este modo.", "permissionDeniedWarning": "Permiso de notificación denegado. El modo No Molestar de la aplicación está activo, pero las notificaciones del sistema no se ven afectadas."}}, "units": {"minutes": "min", "points": "pts", "seconds": "seg"}, "actions": {"back": "Atrás", "backToBlog": "Volver al blog", "backToHome": "Volver al inicio", "backToSessionDetails": "Volver a los detalles", "backToSessions": "Volver a las sesiones", "backToSettings": "Volver a la configuración", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "deleteConfirm": "Confirmar eliminación", "deleting": "Eliminando...", "enterFullscreen": "Pantalla completa", "exitFullscreen": "Salir de pantalla completa", "ok": "OK", "pause": "Pausa", "play": "Reproducir", "preview": "Vista previa", "restart": "Reiniciar", "startSession": "<PERSON><PERSON><PERSON>", "stopPreview": "Detener", "stopTest": "Detener prueba", "testSound": "Probar sonido", "testVoice": "Probar voz", "upload": "Subir"}, "menu": {"navigation": "Navegación", "account": "C<PERSON><PERSON>"}, "notFound": {"message": "Lo siento, la página que buscas no existe.", "backHome": "Volver al inicio"}, "quiz": {"title": "Quiz", "description": "¡Selecciona un quiz para comenzar a probar tus conocimientos!", "comingSoon": "¡Los quiz llegan pronto! Mantente atento."}, "history": {"title": "Historial", "description": "Consulta tus resultados y progreso.", "comingSoon": "El historial estará disponible pronto."}, "categories": {"title": "Categorías", "description": "Elige una categoría para explorar los quiz relacionados.", "comingSoon": "¡Las categorías llegan pronto!"}, "languages": {"french": "Français", "english": "English", "spanish": "Español"}, "notifications": {"notSupported": "Este navegador no soporta notificaciones."}, "pseudoGenerator": {"adjectives": {"light": "<PERSON><PERSON><PERSON>", "wind": "<PERSON><PERSON><PERSON>", "ocean": "Oceánico", "mountain": "<PERSON><PERSON><PERSON><PERSON>", "star": "Estelar", "forest": "Forestal", "river": "Fluvial", "sun": "Solar", "moon": "Lunar", "aurora": "Boreal", "calm": "Calmo", "serene": "<PERSON><PERSON>", "wise": "Sabio", "peaceful": "Pacífico", "gentle": "Suave", "silent": "<PERSON><PERSON><PERSON><PERSON>", "golden": "<PERSON><PERSON>", "silver": "Plateado", "deep": "Profundo", "vast": "Vasto", "subtle": "<PERSON><PERSON>", "vibrant": "<PERSON>ib<PERSON><PERSON>", "fluid": "Fluido", "whispering": "<PERSON><PERSON><PERSON><PERSON>", "ancient": "Antigu<PERSON>", "young": "<PERSON><PERSON>", "free": "Libre", "open": "<PERSON>bie<PERSON>o", "sparkling": "Centelleante", "radiant": "<PERSON><PERSON><PERSON>", "mystic": "Místico", "ethereal": "Etéreo", "luminous": "<PERSON><PERSON><PERSON>", "tranquil": "Tran<PERSON>lo", "velvety": "Terciopelado", "crisp": "<PERSON><PERSON><PERSON><PERSON>", "harmonious": "<PERSON><PERSON><PERSON>", "celestial": "Celestial", "gleaming": "<PERSON><PERSON><PERSON>", "serendipitous": "Fortuito", "enchanting": "Encan<PERSON><PERSON>", "pristine": "<PERSON><PERSON><PERSON><PERSON>", "graceful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "infinite": "Infinito", "tender": "Tierno", "radiating": "<PERSON><PERSON><PERSON>", "soothing": "Calmante", "blissful": "<PERSON><PERSON><PERSON>", "majestic": "<PERSON><PERSON><PERSON><PERSON>", "lively": "Vivaz", "noble": "<PERSON>", "pure": "<PERSON><PERSON>", "elegant": "Elegante", "divine": "<PERSON><PERSON><PERSON>", "resplendent": "Resplandeciente", "seraphic": "Seráfico", "venerable": "Venerable", "exquisite": "Exquisito", "buoyant": "Flotante", "zestful": "<PERSON><PERSON><PERSON><PERSON>", "iridescent": "Iridiscente", "poised": "Equilibrado", "delicate": "Delicado", "fervent": "<PERSON><PERSON><PERSON><PERSON>", "alluring": "Seductor", "spirited": "Enérgico", "dazzling": "Deslumbrante", "tranquilizing": "Tranquilizante", "refined": "Refinado", "captivating": "Cautivador", "lustrous": "<PERSON><PERSON><PERSON>", "vivid": "<PERSON>í<PERSON><PERSON>", "prismatic": "Prismático", "dynamic": "Dinámico", "charming": "Encan<PERSON><PERSON>", "idyllic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "melodic": "Melódico", "dreamy": "<PERSON><PERSON><PERSON>", "picturesque": "Pintoresco", "twinkling": "<PERSON><PERSON><PERSON><PERSON>", "blazing": "<PERSON><PERSON><PERSON>", "sublime": "Sublime"}, "nouns": {"serene": "Serenidad", "calm": "Calma", "wise": "Sabiduría", "peaceful": "Paz", "clairvoyant": "Clarividencia", "harmonious": "Armonía", "awakened": "<PERSON><PERSON><PERSON>", "free": "Libertad", "creative": "Creatividad", "intuitive": "Intuición", "spirit": "Espíritu", "dreamer": "<PERSON><PERSON><PERSON>", "seeker": "Buscador", "wanderer": "Viajero", "healer": "Sanador", "guide": "Guía", "messenger": "Mensajero", "guardian": "Guardián", "listener": "Oyente", "explorer": "Explorador", "observer": "Observador", "anchor": "<PERSON><PERSON><PERSON>", "beacon": "Faro", "pillar": "<PERSON><PERSON>", "essence": "E<PERSON>cia", "echo": "Eco", "source": "Fuente", "haven": "Refugio", "path": "Camino", "horizon": "Horizonte", "breeze": "<PERSON><PERSON><PERSON>", "flame": "Llama", "sky": "Cielo", "dawn": "<PERSON><PERSON><PERSON>", "twilight": "<PERSON>re<PERSON><PERSON><PERSON><PERSON>", "cloud": "Nube", "stream": "<PERSON>", "meadow": "Pradera", "valley": "Valle", "summit": "Cima", "wave": "<PERSON><PERSON>", "spark": "Chi<PERSON><PERSON>", "shadow": "Sombra", "lightning": "Relámpago", "thunder": "<PERSON><PERSON>", "mist": "<PERSON><PERSON><PERSON>", "rainbow": "<PERSON><PERSON><PERSON><PERSON>", "glade": "<PERSON><PERSON><PERSON>", "tide": "<PERSON><PERSON>", "oasis": "Oasis", "ridge": "Crest<PERSON>", "bloom": "Floración", "ember": "<PERSON><PERSON><PERSON>", "glimmer": "<PERSON><PERSON><PERSON>", "cascade": "Cascada", "zephyr": "Céfiro", "reef": "Arrecife", "canyon": "Cañón", "drift": "Deriva", "blaze": "<PERSON><PERSON><PERSON>", "vista": "Vista", "current": "<PERSON><PERSON><PERSON>", "sparkle": "<PERSON><PERSON><PERSON>", "whirlwind": "Torbellino", "sanctuary": "Santuario", "journey": "<PERSON><PERSON>", "destiny": "<PERSON><PERSON>", "pulse": "<PERSON><PERSON><PERSON>", "radiance": "<PERSON><PERSON><PERSON><PERSON>", "silence": "<PERSON><PERSON><PERSON>", "bounty": "Abundancia", "mystery": "Mister<PERSON>", "legend": "Leyenda", "vision": "Visión", "dream": "<PERSON><PERSON>", "hope": "Esperanza", "gleam": "<PERSON><PERSON><PERSON>", "quest": "Búsqueda", "infinity": "Infinito", "sanctum": "Santuario", "flow": "F<PERSON>jo", "heart": "Corazón"}}, "app": {"name": "Piknowkyo", "theme_light": "Cambiar a tema claro", "theme_dark": "Cambiar a tema oscuro", "logo_alt": "Logotipo de Piknowkyo"}, "auth": {"common": {"email_placeholder": "Correo electrónico", "password_placeholder": "Contraseña", "or_separator": "O", "please_wait_loading": "<PERSON>spere por favor...", "success_redirect": "¡Éxito! Redirigiendo..."}, "login": {"subtitle": "¡Bienvenido de nuevo!", "button": "In<PERSON><PERSON>", "button_loading": "Iniciando se<PERSON>...", "google_button": "Iniciar se<PERSON><PERSON> con Google", "google_button_loading": "Iniciando sesión con Google...", "error_invalid_credentials": "Correo electrónico o contraseña incorrectos.", "error_google_popup_closed": "La ventana de inicio de sesión de Google se cerró. Inténtelo de nuevo.", "error_google_popup_cancelled": "Una solicitud de ventana emergente de Google ya está en curso o fue cancelada. Inténtelo de nuevo.", "error_general": "Error al iniciar sesión. Inténtelo de nuevo.", "toggle_signup": "¿Todavía no tienes una cuenta? Regístrate"}, "signup": {"subtitle": "¡<PERSON>rea tu cuenta!", "confirm_password_placeholder": "Confirmar con<PERSON>", "button": "Registrarse", "button_loading": "Registrándose...", "google_button": "Registrarse con Google", "google_button_loading": "Registrándose con Google...", "error_password_mismatch": "Las contraseñas no coinciden.", "error_email_in_use": "Este correo electrónico ya está en uso. Por favor, inicia sesión.", "error_weak_password": "La contraseña es demasiado débil (mínimo 6 caracteres).", "error_general": "Error al registrarse. Inténtelo de nuevo.", "toggle_login": "¿Ya tienes una cuenta? Inicia sesión"}, "logout": {"button": "<PERSON><PERSON><PERSON>", "button_aria_label": "<PERSON><PERSON><PERSON> sesi<PERSON> de tu cuenta"}}, "preferences": {"language": {"question": "Elige tu idioma preferido"}, "notifications": {"question": "¿Te gustaría recibir notificaciones de motivación?"}, "premium": {"question": "¿Te gustaría probar las funciones premium gratis (con anuncios no intrusivos)?"}, "yes": "Sí", "no": "No", "thanks": "¡<PERSON><PERSON><PERSON>!", "validate": "Validar mis preferencias"}, "questionnaire": {"goal": {"question": "¿Cuál es tu objetivo principal?", "relaxation": "Relajación", "confidence": "Confianza en uno mismo", "stress": "Gestión del estrés", "spirituality": "Espiritualidad", "other": "<PERSON><PERSON>"}, "experience": {"question": "¿Has practicado alguna vez hipnosis o meditación?", "never": "Nunca", "sometimes": "A veces", "regularly": "Regularmente"}, "audio": {"question": "¿Prefieres una sesión con música, sonidos naturales o silencio?", "music": "Música", "nature": "Sonidos naturales", "silence": "<PERSON><PERSON><PERSON>"}, "thanks": "¡<PERSON><PERSON><PERSON>!", "viewSuggestions": "Ver mis sugerencias"}, "notificationTest": {"heading": "Prueba de notificaciones", "platform": "Plataforma actual:", "status": "Estado de las notificaciones web:", "title": "Prueba de notificación", "body": "Esta es una prueba de notificación desde PiKnowKyo", "sendButton": "Enviar notificación de prueba"}, "reduxExample": {"loading": "Cargando sesion<PERSON>...", "title": "Ejemplo Redux - Sesiones", "reduxState": "Estado Redux:"}, "audioAssets": {"title": "Gestión de archivos de audio", "musicTitle": "Música", "ambientTitle": "Sonidos ambientales", "noMusics": "No hay música disponible", "noAmbiants": "No hay sonidos ambientales disponibles", "selectFile": "Seleccionar archivo", "changeFile": "Cambiar archivo", "uploadMusicPrompt": "Subir nueva música", "uploadAmbientPrompt": "Subir nuevo sonido ambiental", "uploading": "Subiendo...", "uploadSuccess": "¡Archivo {{fileName}} subido con éxito!", "uploadError": "Error al subir", "previewError": "No se puede reproducir la vista previa de audio", "cannotDeleteDefault": "Los archivos predeterminados no se pueden eliminar", "confirmDeleteTitle": "Confirmar eliminación", "confirmDeleteMessage": "¿Estás seguro de que quieres eliminar este archivo?", "deleteSuccess": "Archivo eliminado con éxito", "deleteError": "Error al eliminar"}, "audioConfig": {"webAudioNotSupported": "Web Audio API no compatible con tu navegador.", "musicTitle": "Música de Fondo", "musicTooltip": "Elige música ambiental para acompañar tu sesión. Puedes ajustar el volumen.", "ambientTitle": "Sonidos Ambientales", "ambientTooltip": "Añade sonidos naturales o ambientales para crear la atmósfera perfecta.", "binauralBeats": "Sonidos Binaurales / Isocrónicos", "binauralTooltip": "Genera sonidos para influir en las ondas cerebrales. Requiere auriculares para el efecto binaural óptimo.", "ttsTitle": "Sín<PERSON>is <PERSON>", "ttsTooltip": "Ajusta el volumen de la voz del guía. El tipo de voz y el idioma se gestionan en la configuración general de la aplicación.", "musicSound": "Música:", "ambientSound": "Sonido ambiental:", "volume": "Volumen", "baseFrequency": "Frecuencia base (Hz)", "baseFreqPresets": "Preajustes de Frec. Base:", "beatFrequency": "<PERSON><PERSON><PERSON> (Hz)", "brainwavePresets": "Preajustes de Ondas Cerebrales (Latido):", "selectPreset": "-- Elegir un preajuste --", "selectState": "-- Elegir un estado --", "targetFrequencyInfo": "<PERSON><PERSON><PERSON>q: {{leftEar}} Hz, <PERSON><PERSON><PERSON>: {{rightEar}} Hz", "headphonesRequired": "Requiere auriculares para el efecto binaural"}, "errors": {"missingPostId": "ID de mensaje faltante.", "postNotFound": "Mensaje no encontrado.", "cantLoadPost": "No se puede cargar el mensaje.", "cantLoadComments": "No se pueden cargar los comentarios.", "cantAddComment": "Error al agregar comentario.", "userNotAuthenticated": "Debes estar conectado para realizar esta acción.", "manageSubscriptionError": "No se puede acceder a la gestión de suscripción.", "paymentError": "Error al procesar el pago.", "cantAddPost": "Error al publicar el mensaje.", "cantLoadSessions": "No se pueden cargar los datos de las sesiones."}, "player": {"sessionEnded": "Sesión terminada.", "readyToStart": "Listo para comenzar...", "audioSettings": "<PERSON><PERSON><PERSON><PERSON>", "volumeControls": "Controles de Volumen", "music": "Música", "ambient": "Ambiente", "voice": "Voz", "binaural": "Ritmos binaurales"}, "test": {"newSongTitle": "Título de nueva canción", "addNewSong": "Agregar nueva canción"}, "sync": {"offline": "Sin conexión", "syncing": "Sincronizando...", "error": "Error de sincronización ({{count}})", "pending": "{{count}} pendientes", "synchronized": "Sincronizado", "syncedMinutesAgo": "Sin<PERSON><PERSON><PERSON><PERSON> hace {{minutes}}min", "syncedHoursAgo": "Sincron<PERSON>do hace {{hours}}h", "online": "En línea", "clickToSync": "Clic para sincronizar"}, "loading": {"user": "Cargando información del usuario...", "profile": "Cargando perfil...", "blog": "Cargando blog...", "comments": "Cargando comentarios...", "post": "Cargando mensaje...", "leaderboard": "Cargando clasificación...", "stats": "Cargando estadísticas..."}, "plans": {"free": {"title": "Plan Gratuito", "price": "$0", "currentPlan": "Tu Plan Actual", "switchToFree": "Cambiar al plan Gratuito"}, "premium": {"title": "Piknowkyo Premium", "billedMonthly": "Facturado mensualmente, cancela en cualquier momento.", "manageSub": "Gestionar Suscripción", "subscribe": "Actualizar a Premium"}, "billing": {"month": "mes"}}, "features": {"free": {"baseMeditations": "Acceso a meditaciones e historias básicas", "backgroundMusic": "Música de fondo básica y voces TTS", "stats": "Estadísticas de progreso", "blog": "Acceso al blog comunitario", "leaderboard": "Participación anónima en tabla de clasificación"}, "premium": {"allSessions": "Acceso ilimitado a TODAS las sesiones (hipnosis, PNL, etc.)", "ambientSounds": "Sonidos ambientales y binaurales avanzados", "customSessions": "<PERSON><PERSON>r sesiones personalizadas", "games": "Acceso a mini-juegos de atención plena", "journal": "Diario de seguimiento detallado", "motivationNotifs": "Notificaciones de motivación personalizadas", "calendar": "Calendario y programas personalizados (próximamente)", "customAudio": "Usar tus propios sonidos y música", "noAds": "Experiencia sin anuncios", "prioritySupport": "Soporte prioritario"}}, "legal": {"privacy": "Política de Privacidad", "terms": "Términos y Condiciones"}, "profile": {"title": "<PERSON>", "notConnectedTitle": "Perfil de Usuario", "pleaseLogin": "Por favor, inicia sesión para acceder a tu perfil.", "publicPseudo": "Nombre de usuario público", "regeneratePseudo": "Generar nuevo nombre de usuario", "premiumMember": "Miembro Premium", "manageSubscription": "Gestionar suscripción", "upgradeToPremium": "Actualizar a Premium", "preferencesTitle": "Preferencias", "appLanguage": "Idioma de la aplicación", "grammaticalGenderLabel": "¿Cómo prefieres que te dirijan en los scripts?", "grammaticalGenderInfo": "Esto nos ayudará a adaptar ciertos textos para una experiencia más personalizada.", "quickActions": "Acciones rápidas", "goToSettings": "Configuración avanzada", "viewStats": "Ver mis estadísticas", "accountActionsTitle": "Gestión de Cuenta", "logout": "<PERSON><PERSON><PERSON>", "deleteAccount": "Eliminar mi cuenta", "deleteConfirmTitle": "Confirmar <PERSON>", "deleteConfirmMessage": "¿Estás seguro de que quieres eliminar tu cuenta? Todos tus datos, incluyendo tu progreso y notas del diario, serán borrados permanentemente. Esta acción es irreversible.", "accountDeletedSuccess": "Tu cuenta y todos tus datos han sido eliminados.", "stats": {"sessionsCompleted": "Sesiones completadas", "daysStreak": "Días consecutivos", "totalMinutes": "Minutos totales"}}, "gender": {"masculine": "<PERSON><PERSON><PERSON><PERSON>", "feminine": "Femenino"}, "leaderboard": {"title": "Clasificación de Exploradores", "description": "Descubre tu posición entre los miembros activos de la comunidad PiKnowKyo. La clasificación se basa en el compromiso y la progresión (anonimizada para la privacidad).", "noData": "La clasificación aún no está disponible o se está calculando. ¡Vuelve pronto!", "pseudo": "<PERSON><PERSON><PERSON><PERSON> (Anónimo)", "score": "Puntuación", "you": "Tú", "privacyNote": "Tu privacidad es importante. Todos los seudónimos están anonimizados para proteger tu identidad. Tu participación en la clasificación es opcional y puede gestionarse en la configuración de tu perfil."}}